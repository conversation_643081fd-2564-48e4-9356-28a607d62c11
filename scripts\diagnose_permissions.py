#!/usr/bin/env python3
"""
Facebook Token Permissions Diagnostic
Diagnose dan fix permission issues untuk DELETE operations
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import requests
import json
from datetime import datetime
from dotenv import load_dotenv

class PermissionDiagnostic:
    """Diagnose Facebook token permissions"""
    
    def __init__(self):
        """Initialize permission diagnostic"""
        load_dotenv()
        
        self.page_id = os.getenv('PAGE_ID')
        self.access_token = os.getenv('PAGE_ACCESS_TOKEN')
        self.base_url = "https://graph.facebook.com/v18.0"
        
        if not self.page_id or not self.access_token:
            raise ValueError("PAGE_ID dan PAGE_ACCESS_TOKEN harus diset di .env")
    
    def check_token_info(self):
        """Check basic token information"""
        print("🔍 Checking Token Information")
        print("=" * 50)
        
        try:
            # Check token info
            response = requests.get(
                f"{self.base_url}/me",
                params={'access_token': self.access_token}
            )
            
            if response.status_code == 200:
                data = response.json()
                print(f"✅ Token is valid")
                print(f"   Token Type: {data.get('category', 'Unknown')}")
                print(f"   Name: {data.get('name', 'Unknown')}")
                print(f"   ID: {data.get('id', 'Unknown')}")
                
                return True, data
            else:
                print(f"❌ Token validation failed: {response.status_code}")
                try:
                    error_data = response.json()
                    print(f"   Error: {error_data}")
                except:
                    print(f"   Error: {response.text}")
                return False, None
                
        except Exception as e:
            print(f"❌ Error checking token: {str(e)}")
            return False, None
    
    def check_page_permissions(self):
        """Check page-specific permissions"""
        print(f"\n🔍 Checking Page Permissions")
        print("=" * 50)
        
        try:
            # Check page info
            response = requests.get(
                f"{self.base_url}/{self.page_id}",
                params={
                    'access_token': self.access_token,
                    'fields': 'id,name,category,can_post,fan_count,access_token'
                }
            )
            
            if response.status_code == 200:
                data = response.json()
                print(f"✅ Page access successful")
                print(f"   Page Name: {data.get('name', 'Unknown')}")
                print(f"   Page ID: {data.get('id', 'Unknown')}")
                print(f"   Category: {data.get('category', 'Unknown')}")
                print(f"   Can Post: {data.get('can_post', 'Unknown')}")
                print(f"   Followers: {data.get('fan_count', 'Unknown')}")
                
                # Check if page has its own access token
                if 'access_token' in data:
                    print(f"   ✅ Page has dedicated access token")
                else:
                    print(f"   ⚠️ No dedicated page access token")
                
                return True, data
            else:
                print(f"❌ Page access failed: {response.status_code}")
                try:
                    error_data = response.json()
                    print(f"   Error: {error_data}")
                except:
                    print(f"   Error: {response.text}")
                return False, None
                
        except Exception as e:
            print(f"❌ Error checking page: {str(e)}")
            return False, None
    
    def check_token_permissions(self):
        """Check detailed token permissions"""
        print(f"\n🔍 Checking Token Permissions")
        print("=" * 50)
        
        try:
            # Check token permissions
            response = requests.get(
                f"{self.base_url}/me/permissions",
                params={'access_token': self.access_token}
            )
            
            if response.status_code == 200:
                data = response.json()
                permissions = data.get('data', [])
                
                print(f"✅ Token permissions retrieved")
                print(f"   Total permissions: {len(permissions)}")
                
                # Check critical permissions
                critical_permissions = [
                    'pages_manage_posts',
                    'pages_manage_engagement', 
                    'pages_read_engagement',
                    'pages_show_list',
                    'manage_pages'
                ]
                
                granted_permissions = []
                missing_permissions = []
                
                for perm in permissions:
                    permission_name = perm.get('permission')
                    status = perm.get('status')
                    
                    if status == 'granted':
                        granted_permissions.append(permission_name)
                    
                    if permission_name in critical_permissions:
                        if status == 'granted':
                            print(f"   ✅ {permission_name}: {status}")
                        else:
                            print(f"   ❌ {permission_name}: {status}")
                            missing_permissions.append(permission_name)
                
                # Check for missing critical permissions
                for critical in critical_permissions:
                    if critical not in [p.get('permission') for p in permissions]:
                        print(f"   ❌ {critical}: NOT REQUESTED")
                        missing_permissions.append(critical)
                
                return True, {
                    'granted': granted_permissions,
                    'missing': missing_permissions,
                    'all_permissions': permissions
                }
            else:
                print(f"❌ Permission check failed: {response.status_code}")
                return False, None
                
        except Exception as e:
            print(f"❌ Error checking permissions: {str(e)}")
            return False, None
    
    def test_comment_operations(self):
        """Test comment read/write operations"""
        print(f"\n🔍 Testing Comment Operations")
        print("=" * 50)
        
        try:
            # Get posts first
            posts_response = requests.get(
                f"{self.base_url}/{self.page_id}/posts",
                params={
                    'access_token': self.access_token,
                    'limit': 3,
                    'fields': 'id'
                }
            )
            
            if posts_response.status_code != 200:
                print(f"❌ Cannot get posts: {posts_response.status_code}")
                return False
            
            posts = posts_response.json().get('data', [])
            if not posts:
                print(f"❌ No posts found")
                return False
            
            print(f"✅ Found {len(posts)} posts")
            
            # Test comment reading
            post_id = posts[0]['id']
            comments_response = requests.get(
                f"{self.base_url}/{post_id}/comments",
                params={
                    'access_token': self.access_token,
                    'limit': 5,
                    'fields': 'id,message,from'
                }
            )
            
            if comments_response.status_code == 200:
                comments = comments_response.json().get('data', [])
                print(f"✅ Can read comments: {len(comments)} found")
                
                if comments:
                    # Test comment creation
                    test_comment_response = requests.post(
                        f"{self.base_url}/{post_id}/comments",
                        params={
                            'access_token': self.access_token,
                            'message': f'Test comment for permission check - {datetime.now().strftime("%H:%M:%S")}'
                        }
                    )
                    
                    if test_comment_response.status_code == 200:
                        test_comment_data = test_comment_response.json()
                        test_comment_id = test_comment_data.get('id')
                        print(f"✅ Can create comments: {test_comment_id}")
                        
                        # Test comment deletion
                        delete_response = requests.delete(
                            f"{self.base_url}/{test_comment_id}",
                            params={'access_token': self.access_token}
                        )
                        
                        if delete_response.status_code == 200:
                            print(f"✅ Can delete comments: SUCCESS")
                            return True
                        else:
                            print(f"❌ Cannot delete comments: {delete_response.status_code}")
                            try:
                                error_data = delete_response.json()
                                print(f"   Delete Error: {error_data}")
                            except:
                                print(f"   Delete Error: {delete_response.text}")
                            return False
                    else:
                        print(f"❌ Cannot create comments: {test_comment_response.status_code}")
                        try:
                            error_data = test_comment_response.json()
                            print(f"   Create Error: {error_data}")
                        except:
                            print(f"   Create Error: {test_comment_response.text}")
                        return False
                else:
                    print(f"⚠️ No existing comments to test deletion")
                    return True
            else:
                print(f"❌ Cannot read comments: {comments_response.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ Error testing operations: {str(e)}")
            return False
    
    def generate_permission_report(self):
        """Generate comprehensive permission report"""
        print(f"\n📋 PERMISSION DIAGNOSTIC REPORT")
        print("=" * 60)
        
        # Test all components
        token_valid, token_data = self.check_token_info()
        page_valid, page_data = self.check_page_permissions()
        perms_valid, perms_data = self.check_token_permissions()
        operations_valid = self.test_comment_operations()
        
        print(f"\n📊 Summary:")
        print(f"   Token Valid: {'✅' if token_valid else '❌'}")
        print(f"   Page Access: {'✅' if page_valid else '❌'}")
        print(f"   Permissions Check: {'✅' if perms_valid else '❌'}")
        print(f"   Comment Operations: {'✅' if operations_valid else '❌'}")
        
        # Recommendations
        print(f"\n💡 Recommendations:")
        
        if not token_valid:
            print(f"   🚨 CRITICAL: Token is invalid - regenerate access token")
        
        if not page_valid:
            print(f"   🚨 CRITICAL: Cannot access page - check page ID and permissions")
        
        if perms_valid and perms_data:
            missing = perms_data.get('missing', [])
            if missing:
                print(f"   ⚠️ Missing permissions: {', '.join(missing)}")
                print(f"   💡 Request these permissions in Facebook App settings")
        
        if not operations_valid:
            print(f"   🚨 CRITICAL: Comment operations failed")
            print(f"   💡 Check token scope and page role")
            print(f"   💡 Ensure you are Admin/Editor of the page")
        
        # Token regeneration guide
        if not token_valid or not operations_valid:
            print(f"\n🔧 Token Regeneration Guide:")
            print(f"   1. Go to Facebook Developers Console")
            print(f"   2. Select your app")
            print(f"   3. Go to Tools > Graph API Explorer")
            print(f"   4. Select your page")
            print(f"   5. Request these permissions:")
            print(f"      - pages_manage_posts")
            print(f"      - pages_manage_engagement")
            print(f"      - pages_read_engagement")
            print(f"      - pages_show_list")
            print(f"   6. Generate new access token")
            print(f"   7. Update .env file with new token")
        
        return {
            'token_valid': token_valid,
            'page_valid': page_valid,
            'permissions_valid': perms_valid,
            'operations_valid': operations_valid,
            'token_data': token_data,
            'page_data': page_data,
            'permissions_data': perms_data
        }

def main():
    """Main diagnostic function"""
    print("🔍 Facebook Token Permissions Diagnostic")
    print("=" * 60)
    
    try:
        diagnostic = PermissionDiagnostic()
        report = diagnostic.generate_permission_report()
        
        # Overall status
        all_valid = all([
            report['token_valid'],
            report['page_valid'], 
            report['permissions_valid'],
            report['operations_valid']
        ])
        
        if all_valid:
            print(f"\n🎉 ALL CHECKS PASSED!")
            print(f"✅ Your token has all required permissions")
            print(f"✅ Comment moderation should work properly")
        else:
            print(f"\n⚠️ ISSUES DETECTED!")
            print(f"❌ Some permission checks failed")
            print(f"💡 Follow the recommendations above to fix issues")
        
    except Exception as e:
        print(f"\n❌ Diagnostic failed: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
