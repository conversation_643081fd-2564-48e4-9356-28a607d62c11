#!/usr/bin/env python3
"""
Optimize Settings for Facebook App Rate Limits
Configure optimal settings berdasarkan app-level rate limits
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import time
from datetime import datetime, timed<PERSON><PERSON>

def generate_optimized_env_config():
    """Generate optimized .env configuration"""
    print("🔧 Generating Optimized Configuration for App Rate Limits")
    print("=" * 60)
    
    # Ultra-conservative settings untuk app-level rate limits
    config = {
        # Rate limiting settings
        'FACEBOOK_DELETE_RATE_LIMIT': '10',           # 10 deletes per minute (0.17 RPS)
        'FACEBOOK_READ_RATE_LIMIT': '60',             # 60 reads per minute (1 RPS)
        'FACEBOOK_MIN_DELETE_DELAY': '6.0',           # 6 seconds between deletes
        'FACEBOOK_MIN_READ_DELAY': '1.0',             # 1 second between reads
        
        # Retry settings
        'FACEBOOK_MAX_RETRIES': '5',                  # More retries
        'FACEBOOK_RETRY_DELAY': '10',                 # 10 seconds base retry delay
        'FACEBOOK_EXPONENTIAL_BACKOFF': 'true',       # Enable exponential backoff
        
        # Monitoring settings
        'FACEBOOK_RATE_LIMIT_MONITORING': 'true',     # Enable monitoring
        'FACEBOOK_LOG_RATE_LIMITS': 'true',           # Log rate limit events
        'FACEBOOK_ALERT_THRESHOLD': '50',             # Alert at 50% usage
        
        # App-level settings
        'FACEBOOK_APP_TIER': 'basic',                 # Assume basic tier
        'FACEBOOK_HOURLY_LIMIT': '200',               # Conservative hourly limit
        'FACEBOOK_DAILY_LIMIT': '2000',               # Conservative daily limit
        
        # Operational settings
        'AUTO_DELETE_SPAM': 'false',                  # Disable auto-delete initially
        'MANUAL_MODERATION_ONLY': 'true',             # Enable manual mode only
        'BATCH_SIZE': '5',                            # Small batch sizes
        'SCAN_INTERVAL': '300',                       # 5 minutes between scans
    }
    
    print("📋 Recommended .env Settings:")
    print("-" * 40)
    
    for key, value in config.items():
        print(f"{key}={value}")
    
    print(f"\n💡 Configuration Explanation:")
    print("=" * 40)
    print("🐌 ULTRA-CONSERVATIVE SETTINGS:")
    print("   - DELETE: 0.17 RPS (10/minute) - Very slow but safe")
    print("   - READ: 1.0 RPS (60/minute) - Conservative")
    print("   - Delays: 6s between deletes, 1s between reads")
    print("   - Auto-delete: DISABLED - Manual only")
    
    print(f"\n⚠️ App Rate Limit Strategy:")
    print("   - Assume Facebook App Basic Tier")
    print("   - Conservative hourly limits (200 requests)")
    print("   - Extended retry delays (10s base)")
    print("   - Exponential backoff enabled")
    
    print(f"\n🔄 Operational Mode:")
    print("   - Manual moderation only")
    print("   - Small batch sizes (5 comments)")
    print("   - Long scan intervals (5 minutes)")
    print("   - Extensive logging and monitoring")
    
    return config

def create_rate_limit_recovery_script():
    """Create script untuk recovery dari rate limits"""
    
    recovery_script = '''#!/usr/bin/env python3
"""
Facebook Rate Limit Recovery Script
Handle recovery dari app-level rate limits
"""

import time
import requests
from datetime import datetime, timedelta
from dotenv import load_dotenv
import os

def check_app_rate_limit_status():
    """Check if app rate limits have reset"""
    load_dotenv()
    
    page_id = os.getenv('PAGE_ID')
    access_token = os.getenv('PAGE_ACCESS_TOKEN')
    
    if not page_id or not access_token:
        print("❌ Missing credentials")
        return False
    
    try:
        # Simple test request
        response = requests.get(
            f"https://graph.facebook.com/v18.0/{page_id}",
            params={'access_token': access_token, 'fields': 'id'},
            timeout=10
        )
        
        if response.status_code == 200:
            print("✅ App rate limits appear to be reset")
            return True
        elif response.status_code == 403:
            error_data = response.json()
            error_code = error_data.get('error', {}).get('code', 0)
            
            if error_code == 4:
                print("❌ App rate limit still active")
                return False
            else:
                print(f"❌ Other permission error: {error_code}")
                return False
        else:
            print(f"⚠️ Unexpected status: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Error checking status: {str(e)}")
        return False

def wait_for_rate_limit_reset(max_wait_hours=2):
    """Wait for rate limits to reset"""
    print(f"⏰ Waiting for Facebook App rate limits to reset...")
    print(f"Max wait time: {max_wait_hours} hours")
    
    start_time = datetime.now()
    end_time = start_time + timedelta(hours=max_wait_hours)
    
    check_interval = 300  # 5 minutes
    
    while datetime.now() < end_time:
        if check_app_rate_limit_status():
            elapsed = (datetime.now() - start_time).total_seconds() / 60
            print(f"🎉 Rate limits reset after {elapsed:.1f} minutes!")
            return True
        
        remaining = (end_time - datetime.now()).total_seconds() / 60
        print(f"⏰ Still rate limited. Checking again in 5 minutes. ({remaining:.0f}min remaining)")
        time.sleep(check_interval)
    
    print(f"⏰ Max wait time reached. Rate limits may still be active.")
    return False

if __name__ == "__main__":
    print("🔄 Facebook Rate Limit Recovery")
    print("=" * 40)
    
    if check_app_rate_limit_status():
        print("✅ No rate limits detected. Safe to proceed.")
    else:
        print("⚠️ Rate limits detected. Starting recovery process...")
        wait_for_rate_limit_reset()
'''
    
    with open('scripts/rate_limit_recovery.py', 'w') as f:
        f.write(recovery_script)
    
    print(f"💾 Created: scripts/rate_limit_recovery.py")

def generate_production_recommendations():
    """Generate production deployment recommendations"""
    print(f"\n🚀 PRODUCTION DEPLOYMENT RECOMMENDATIONS")
    print("=" * 60)
    
    print("📋 IMMEDIATE ACTIONS (Next 1 Hour):")
    print("1. ⏰ WAIT for app rate limits to reset (typically 1 hour)")
    print("2. 🔧 Update .env with ultra-conservative settings")
    print("3. 🧪 Test with rate_limit_recovery.py script")
    print("4. 📊 Monitor with very low request rates")
    
    print(f"\n📋 SHORT-TERM ACTIONS (Next 24 Hours):")
    print("1. 📈 Check Facebook App Dashboard for usage analytics")
    print("2. 🔍 Verify app tier (Basic/Standard/Advanced)")
    print("3. 📝 Consider app tier upgrade if needed")
    print("4. 🧪 Gradually increase limits if no rate limiting")
    
    print(f"\n📋 LONG-TERM STRATEGY:")
    print("1. 📊 Implement comprehensive monitoring")
    print("2. 🔄 Use adaptive rate limiting based on app usage")
    print("3. 📈 Scale up gradually based on actual limits")
    print("4. 🏗️ Consider multiple apps for high-volume scenarios")
    
    print(f"\n⚠️ CRITICAL SETTINGS FOR APP RATE LIMITS:")
    print("   - Start with 0.1 RPS DELETE operations")
    print("   - Use 10+ second delays between operations")
    print("   - Enable extensive retry logic")
    print("   - Monitor app-level usage metrics")
    print("   - Disable auto-deletion until limits are understood")

def main():
    """Main function"""
    print("🔧 Facebook App Rate Limit Optimizer")
    print("=" * 60)
    
    # Generate optimized configuration
    config = generate_optimized_env_config()
    
    # Create recovery script
    create_rate_limit_recovery_script()
    
    # Generate recommendations
    generate_production_recommendations()
    
    print(f"\n🎯 NEXT STEPS:")
    print("1. Copy the .env settings above to your .env file")
    print("2. Wait 1 hour for Facebook app limits to reset")
    print("3. Run: python scripts/rate_limit_recovery.py")
    print("4. Test with ultra-conservative settings")
    print("5. Gradually optimize based on monitoring data")
    
    print(f"\n💡 KEY INSIGHT:")
    print("Your issue is APP-LEVEL rate limiting, not API-level!")
    print("This requires much more conservative approach than normal rate limiting.")

if __name__ == "__main__":
    main()
