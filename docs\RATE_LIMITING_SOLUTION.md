# Facebook Graph API Rate Limiting Solution

Solusi komprehensif untuk mengatasi rate limiting pada Facebook Graph API dalam aplikasi moderasi komentar.

## 🔍 **ROOT CAUSE ANALYSIS**

### **<PERSON><PERSON><PERSON> yang <PERSON>ukan:**

#### **1. Perbedaan Rate Limits berdasarkan Operation Type:**
| Operation | **Rate Limit** | **Test Results** | **Production Impact** |
|-----------|----------------|------------------|----------------------|
| **READ (GET)** | ~10+ RPS | ✅ No limits detected | ✅ Works fine |
| **DELETE** | **~1-2 RPS** | ✅ 2 RPS OK, but... | ❌ **Rate limited in production** |

#### **2. Cumulative Usage Over Time:**
- **Test Environment**: 25 DELETE operations over 10 minutes
- **Production Environment**: **Hundreds** of DELETE operations over hours
- **Facebook Rate Limits**: Applied per **hour/day**, not per second

#### **3. Business Usage Headers Analysis:**
```json
{
  "business_usage": {
    "call_count": 3,      // Test: 3%, Production: 80-100%
    "total_time": 2,      // Test: 2%, Production: 80-100%
    "total_cputime": 1,   // Test: 1%, Production: varies
    "estimated_time_to_regain_access": 0
  }
}
```

## 🛠️ **IMPLEMENTED SOLUTIONS**

### **1. Rate Limiter Implementation**

#### **File:** `src/utils/rate_limiter.py`

**Features:**
- ✅ Separate limits untuk READ dan DELETE operations
- ✅ Thread-safe implementation
- ✅ Adaptive rate limiting berdasarkan Facebook headers
- ✅ Automatic backoff pada rate limit detection
- ✅ Usage statistics tracking

**Configuration:**
```python
# Conservative settings (production-safe)
DELETE_LIMIT_PER_MINUTE = 30    # 0.5 RPS
DELETE_LIMIT_PER_HOUR = 1000    # ~0.28 RPS average
READ_LIMIT_PER_MINUTE = 300     # 5 RPS
MIN_DELETE_DELAY = 2.0          # 2 seconds between deletes
MIN_READ_DELAY = 0.2            # 0.2 seconds between reads
```

### **2. Facebook API Service Updates**

#### **File:** `src/app/streamlit_facebook.py`

**Changes:**
- ✅ Added `@rate_limited_delete` decorator untuk DELETE operations
- ✅ Added `@rate_limited_read` decorator untuk READ operations
- ✅ Enhanced rate limit header parsing
- ✅ Adaptive retry logic dengan Facebook Retry-After headers

**Usage:**
```python
@rate_limited_delete
def delete_comment(self, comment_id: str) -> bool:
    # Rate limiting handled automatically
    # Original delete logic here
    pass

@rate_limited_read
def get_post_comments(self, post_id: str) -> List[Dict]:
    # Rate limiting handled automatically
    # Original read logic here
    pass
```

### **3. Production Monitoring**

#### **File:** `scripts/monitor_production_limits.py`

**Features:**
- ✅ Real-time rate limit monitoring
- ✅ Usage trend analysis
- ✅ Automatic alerting pada high usage
- ✅ Detailed reporting dengan JSON export
- ✅ Configurable monitoring intervals

## 📊 **RECOMMENDED PRODUCTION SETTINGS**

### **🔧 Conservative Settings (Recommended Start):**
```python
# Rate limits
DELETE_OPERATIONS_PER_MINUTE = 20    # 0.33 RPS
DELETE_OPERATIONS_PER_HOUR = 800     # ~0.22 RPS average
MINIMUM_DELETE_DELAY = 3.0           # 3 seconds between deletes

# Monitoring thresholds
CALL_COUNT_WARNING = 60%             # Alert at 60% usage
CALL_COUNT_CRITICAL = 80%            # Critical at 80% usage
TIME_USAGE_WARNING = 70%             # Alert at 70% time usage
```

### **🚀 Optimized Settings (After Monitoring):**
```python
# After confirming no rate limiting
DELETE_OPERATIONS_PER_MINUTE = 40    # 0.67 RPS
DELETE_OPERATIONS_PER_HOUR = 1200    # ~0.33 RPS average
MINIMUM_DELETE_DELAY = 2.0           # 2 seconds between deletes
```

### **⚡ Aggressive Settings (High Volume Pages):**
```python
# Only if monitoring shows consistent low usage
DELETE_OPERATIONS_PER_MINUTE = 60    # 1.0 RPS
DELETE_OPERATIONS_PER_HOUR = 1800    # ~0.5 RPS average
MINIMUM_DELETE_DELAY = 1.0           # 1 second between deletes
```

## 🚀 **IMPLEMENTATION STEPS**

### **Step 1: Deploy Rate Limiter**
```bash
# Rate limiter sudah tersedia di src/utils/rate_limiter.py
# Facebook API service sudah diupdate dengan decorators
```

### **Step 2: Test in Development**
```bash
# Test rate limiter functionality
python scripts/test_delete_limits.py

# Monitor rate limits
python scripts/monitor_production_limits.py
```

### **Step 3: Deploy to Production**
```bash
# Deploy dengan conservative settings
# Monitor usage selama 24 jam pertama
```

### **Step 4: Monitor and Optimize**
```bash
# Run production monitoring
python scripts/monitor_production_limits.py

# Analyze reports dan adjust settings
```

## 📈 **MONITORING AND ALERTING**

### **Real-time Monitoring:**
```bash
# Quick check (5 minutes)
python scripts/monitor_production_limits.py
# Select option 1

# Standard monitoring (30 minutes)
python scripts/monitor_production_limits.py
# Select option 2

# Extended monitoring (60 minutes)
python scripts/monitor_production_limits.py
# Select option 3
```

### **Alert Thresholds:**
| Metric | **Warning** | **Critical** | **Action** |
|--------|-------------|--------------|------------|
| Call Count | 60% | 80% | Reduce DELETE frequency |
| Time Usage | 70% | 85% | Optimize request patterns |
| Rate Limit Events | 1/hour | 3/hour | Increase delays |

### **Automated Responses:**
- **60-79% usage**: Increase DELETE delay by 25%
- **80-89% usage**: Increase DELETE delay by 50%
- **90%+ usage**: Increase DELETE delay by 100%

## 🔧 **CONFIGURATION OPTIONS**

### **Environment Variables:**
```env
# Add to .env file
FACEBOOK_DELETE_RATE_LIMIT=30        # Deletes per minute
FACEBOOK_READ_RATE_LIMIT=300         # Reads per minute
FACEBOOK_MIN_DELETE_DELAY=2.0        # Minimum seconds between deletes
FACEBOOK_RATE_LIMIT_MONITORING=true  # Enable monitoring
```

### **Runtime Configuration:**
```python
from src.utils.rate_limiter import get_rate_limiter

# Get rate limiter instance
rate_limiter = get_rate_limiter()

# Adjust settings dynamically
rate_limiter.DELETE_LIMIT_PER_MINUTE = 20
rate_limiter.MIN_DELETE_DELAY = 3.0

# Get current usage
stats = rate_limiter.get_usage_stats()
print(f"Current usage: {stats}")
```

## 📊 **EXPECTED PERFORMANCE IMPROVEMENTS**

### **Before Rate Limiting:**
- ❌ Frequent rate limit errors
- ❌ Failed DELETE operations
- ❌ Inconsistent performance
- ❌ No visibility into usage

### **After Rate Limiting:**
- ✅ **0% rate limit errors** (target)
- ✅ **95%+ DELETE success rate**
- ✅ Predictable performance
- ✅ Real-time usage monitoring
- ✅ Automatic adaptation to Facebook limits

### **Capacity Estimates:**
| Page Size | **Comments/Hour** | **DELETE Rate** | **Feasibility** |
|-----------|-------------------|-----------------|-----------------|
| Small | 100 | 0.03 RPS | ✅ Trivial |
| Medium | 1,000 | 0.28 RPS | ✅ Easy |
| Large | 5,000 | 1.4 RPS | ✅ Comfortable |
| Very Large | 10,000 | 2.8 RPS | ⚠️ Monitor closely |
| Viral | 20,000+ | 5.6+ RPS | ❌ Requires optimization |

## 🚨 **TROUBLESHOOTING**

### **Common Issues:**

#### **1. Still Getting Rate Limited:**
```bash
# Check current settings
python -c "from src.utils.rate_limiter import get_rate_limiter; print(get_rate_limiter().get_usage_stats())"

# Increase delays
# Edit src/utils/rate_limiter.py
# Increase MIN_DELETE_DELAY to 5.0
# Decrease DELETE_LIMIT_PER_MINUTE to 10
```

#### **2. Too Slow Performance:**
```bash
# Monitor usage first
python scripts/monitor_production_limits.py

# If usage is low (<50%), gradually increase limits
# If usage is high (>70%), keep conservative settings
```

#### **3. Rate Limiter Not Working:**
```bash
# Check imports
python -c "from src.utils.rate_limiter import rate_limited_delete; print('OK')"

# Check decorator application
# Verify @rate_limited_delete is applied to delete_comment method
```

### **Debug Commands:**
```bash
# Test rate limiter
python -c "
from src.utils.rate_limiter import get_rate_limiter
rl = get_rate_limiter()
print('Can delete:', rl.can_make_delete_request())
print('Stats:', rl.get_usage_stats())
"

# Test Facebook API with rate limiting
python scripts/test_delete_limits.py

# Monitor real-time usage
python scripts/monitor_production_limits.py
```

## 📞 **SUPPORT AND MAINTENANCE**

### **Regular Maintenance:**
1. **Weekly**: Review monitoring reports
2. **Monthly**: Analyze usage trends dan adjust settings
3. **Quarterly**: Review Facebook API changes

### **Performance Tuning:**
1. Start dengan conservative settings
2. Monitor selama 1 week
3. Gradually increase limits jika usage rendah
4. Always maintain safety margin (max 70% usage)

### **Emergency Procedures:**
1. **Rate Limit Storm**: Increase all delays by 200%
2. **API Changes**: Revert to most conservative settings
3. **High Volume Events**: Temporarily disable auto-deletion

## 🎯 **SUCCESS METRICS**

### **Target KPIs:**
- **Rate Limit Errors**: <1% of requests
- **DELETE Success Rate**: >95%
- **Average Response Time**: <3 seconds
- **Usage Efficiency**: 50-70% of Facebook limits
- **Uptime**: >99.5%

### **Monitoring Dashboard:**
- Real-time usage percentages
- Rate limit event tracking
- Performance trends
- Alert notifications
- Capacity planning metrics
