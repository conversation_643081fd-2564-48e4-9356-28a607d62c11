#!/usr/bin/env python3
"""
Facebook DELETE Operations Rate Limit Tester
Test khusus untuk DELETE operations yang memiliki rate limit berbeda
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import time
import requests
import json
from datetime import datetime
from dotenv import load_dotenv

class DeleteLimitTester:
    """Test DELETE operation limits"""
    
    def __init__(self):
        """Initialize delete tester"""
        load_dotenv()
        
        self.page_id = os.getenv('PAGE_ID')
        self.access_token = os.getenv('PAGE_ACCESS_TOKEN')
        self.base_url = "https://graph.facebook.com/v18.0"
        
        # DELETE-specific tracking
        self.delete_stats = {
            'total_attempts': 0,
            'successful_deletes': 0,
            'rate_limited': 0,
            'permission_denied': 0,
            'not_found': 0,
            'other_errors': 0
        }
        
        self.delete_response_times = []
        self.rate_limit_details = []
        
        if not self.page_id or not self.access_token:
            raise ValueError("PAGE_ID dan PAGE_ACCESS_TOKEN harus diset di .env")
    
    def create_test_comment(self, post_id, message="Test comment for deletion"):
        """
        Create a test comment yang bisa dihapus
        
        Args:
            post_id (str): Post ID untuk comment
            message (str): Comment message
            
        Returns:
            str: Comment ID jika berhasil, None jika gagal
        """
        try:
            url = f"{self.base_url}/{post_id}/comments"
            params = {
                'access_token': self.access_token,
                'message': message
            }
            
            response = requests.post(url, params=params, timeout=10)
            
            if response.status_code == 200:
                data = response.json()
                comment_id = data.get('id')
                print(f"✅ Created test comment: {comment_id}")
                return comment_id
            else:
                print(f"❌ Failed to create test comment: {response.status_code}")
                try:
                    error_data = response.json()
                    print(f"   Error: {error_data}")
                except:
                    print(f"   Error: {response.text}")
                return None
                
        except Exception as e:
            print(f"❌ Exception creating test comment: {str(e)}")
            return None
    
    def test_single_delete(self, comment_id, description="delete"):
        """
        Test single DELETE operation
        
        Args:
            comment_id (str): Comment ID to delete
            description (str): Description for logging
            
        Returns:
            tuple: (success, status_code, response_time, error_details)
        """
        start_time = time.time()
        
        try:
            url = f"{self.base_url}/{comment_id}"
            params = {'access_token': self.access_token}
            
            response = requests.delete(url, params=params, timeout=10)
            response_time = time.time() - start_time
            
            self.delete_stats['total_attempts'] += 1
            self.delete_response_times.append(response_time)
            
            if response.status_code == 200:
                self.delete_stats['successful_deletes'] += 1
                print(f"✅ {description}: SUCCESS ({response_time:.3f}s)")
                return True, 200, response_time, None
            
            elif response.status_code == 429:
                self.delete_stats['rate_limited'] += 1
                
                # Extract rate limit info
                rate_limit_info = {
                    'timestamp': datetime.now().isoformat(),
                    'retry_after': response.headers.get('Retry-After'),
                    'x_app_usage': response.headers.get('X-App-Usage'),
                    'x_page_usage': response.headers.get('X-Page-Usage'),
                    'response_time': response_time
                }
                
                self.rate_limit_details.append(rate_limit_info)
                
                print(f"⚠️ {description}: RATE LIMITED ({response_time:.3f}s)")
                print(f"   Retry-After: {rate_limit_info['retry_after']}")
                print(f"   App Usage: {rate_limit_info['x_app_usage']}")
                print(f"   Page Usage: {rate_limit_info['x_page_usage']}")
                
                return False, 429, response_time, rate_limit_info
            
            elif response.status_code == 403:
                self.delete_stats['permission_denied'] += 1
                print(f"❌ {description}: PERMISSION DENIED ({response_time:.3f}s)")
                
                try:
                    error_data = response.json()
                    return False, 403, response_time, error_data
                except:
                    return False, 403, response_time, response.text
            
            elif response.status_code == 404:
                self.delete_stats['not_found'] += 1
                print(f"⚠️ {description}: NOT FOUND ({response_time:.3f}s)")
                return False, 404, response_time, "Comment not found"
            
            else:
                self.delete_stats['other_errors'] += 1
                print(f"❌ {description}: ERROR {response.status_code} ({response_time:.3f}s)")
                
                try:
                    error_data = response.json()
                    return False, response.status_code, response_time, error_data
                except:
                    return False, response.status_code, response_time, response.text
        
        except Exception as e:
            response_time = time.time() - start_time
            self.delete_stats['total_attempts'] += 1
            self.delete_stats['other_errors'] += 1
            self.delete_response_times.append(response_time)
            
            print(f"❌ {description}: EXCEPTION ({response_time:.3f}s) - {str(e)}")
            return False, 0, response_time, str(e)
    
    def test_delete_rate_limits(self, max_deletes=10, delay_between=0.5):
        """
        Test DELETE rate limits dengan berbagai delay
        
        Args:
            max_deletes (int): Maximum number of delete attempts
            delay_between (float): Delay between delete attempts
        """
        print(f"\n🗑️ Testing DELETE Rate Limits")
        print(f"Max deletes: {max_deletes}, Delay: {delay_between}s")
        print("=" * 50)
        
        # Get posts untuk create test comments
        posts_url = f"{self.base_url}/{self.page_id}/posts"
        response = requests.get(posts_url, params={
            'access_token': self.access_token,
            'limit': 5,
            'fields': 'id'
        })
        
        if response.status_code != 200:
            print("❌ Failed to get posts for testing")
            return
        
        posts = response.json().get('data', [])
        if not posts:
            print("❌ No posts found for testing")
            return
        
        print(f"✅ Found {len(posts)} posts for testing")
        
        # Create and delete test comments
        successful_creates = 0
        test_comments = []
        
        print(f"\n📝 Creating test comments...")
        for i in range(min(max_deletes, len(posts) * 2)):  # Create more than we need
            post = posts[i % len(posts)]
            post_id = post['id']
            
            comment_id = self.create_test_comment(
                post_id, 
                f"Test comment #{i+1} for deletion testing - {datetime.now().strftime('%H:%M:%S')}"
            )
            
            if comment_id:
                test_comments.append(comment_id)
                successful_creates += 1
                time.sleep(0.5)  # Delay between creates
            
            if successful_creates >= max_deletes:
                break
        
        print(f"✅ Created {successful_creates} test comments")
        
        if successful_creates == 0:
            print("❌ No test comments created, cannot test DELETE limits")
            return
        
        # Now test DELETE operations
        print(f"\n🗑️ Testing DELETE operations...")
        
        for i, comment_id in enumerate(test_comments):
            print(f"\n{i+1}. Deleting comment {comment_id}...")
            
            success, status, response_time, error = self.test_single_delete(
                comment_id, 
                f"Delete {i+1}/{len(test_comments)}"
            )
            
            # Stop if we hit rate limits consistently
            if status == 429 and i > 2:  # Allow a few rate limits before stopping
                recent_rate_limits = sum(1 for detail in self.rate_limit_details[-3:] 
                                       if detail['timestamp'])
                if recent_rate_limits >= 2:
                    print(f"\n⚠️ Stopping test due to consistent rate limiting")
                    break
            
            # Add delay between deletes
            if i < len(test_comments) - 1:  # Don't delay after last delete
                print(f"   Waiting {delay_between}s before next delete...")
                time.sleep(delay_between)
        
        # Test results
        self.print_delete_results()
    
    def test_different_delete_rates(self):
        """Test DELETE operations dengan berbagai rate"""
        print(f"\n🧪 Testing Different DELETE Rates")
        print("=" * 50)
        
        test_scenarios = [
            {"delay": 2.0, "description": "Conservative (0.5 RPS)"},
            {"delay": 1.0, "description": "Moderate (1.0 RPS)"},
            {"delay": 0.5, "description": "Aggressive (2.0 RPS)"},
            {"delay": 0.2, "description": "Very Aggressive (5.0 RPS)"}
        ]
        
        for scenario in test_scenarios:
            print(f"\n📊 Testing {scenario['description']}")
            print("-" * 30)
            
            # Reset stats for this scenario
            scenario_stats = {
                'total_attempts': 0,
                'successful_deletes': 0,
                'rate_limited': 0
            }
            
            # Test with this delay
            initial_attempts = self.delete_stats['total_attempts']
            initial_success = self.delete_stats['successful_deletes']
            initial_rate_limited = self.delete_stats['rate_limited']
            
            self.test_delete_rate_limits(max_deletes=5, delay_between=scenario['delay'])
            
            # Calculate scenario-specific results
            scenario_stats['total_attempts'] = self.delete_stats['total_attempts'] - initial_attempts
            scenario_stats['successful_deletes'] = self.delete_stats['successful_deletes'] - initial_success
            scenario_stats['rate_limited'] = self.delete_stats['rate_limited'] - initial_rate_limited
            
            if scenario_stats['total_attempts'] > 0:
                success_rate = scenario_stats['successful_deletes'] / scenario_stats['total_attempts'] * 100
                rate_limit_rate = scenario_stats['rate_limited'] / scenario_stats['total_attempts'] * 100
                
                print(f"📊 {scenario['description']} Results:")
                print(f"   Success Rate: {success_rate:.1f}%")
                print(f"   Rate Limited: {rate_limit_rate:.1f}%")
                
                if rate_limit_rate == 0:
                    print(f"   ✅ No rate limiting at this rate")
                elif rate_limit_rate < 20:
                    print(f"   ⚠️ Some rate limiting")
                else:
                    print(f"   🚨 Heavy rate limiting")
            
            # Wait between scenarios
            time.sleep(5)
    
    def print_delete_results(self):
        """Print comprehensive DELETE test results"""
        print(f"\n📊 DELETE OPERATIONS RESULTS")
        print("=" * 50)
        
        total = self.delete_stats['total_attempts']
        if total == 0:
            print("❌ No DELETE operations attempted")
            return
        
        print(f"📈 DELETE Statistics:")
        print(f"   Total Attempts: {total}")
        print(f"   Successful: {self.delete_stats['successful_deletes']} ({self.delete_stats['successful_deletes']/total*100:.1f}%)")
        print(f"   Rate Limited: {self.delete_stats['rate_limited']} ({self.delete_stats['rate_limited']/total*100:.1f}%)")
        print(f"   Permission Denied: {self.delete_stats['permission_denied']} ({self.delete_stats['permission_denied']/total*100:.1f}%)")
        print(f"   Not Found: {self.delete_stats['not_found']} ({self.delete_stats['not_found']/total*100:.1f}%)")
        print(f"   Other Errors: {self.delete_stats['other_errors']} ({self.delete_stats['other_errors']/total*100:.1f}%)")
        
        if self.delete_response_times:
            avg_time = sum(self.delete_response_times) / len(self.delete_response_times)
            print(f"\n⏱️ DELETE Performance:")
            print(f"   Average Response Time: {avg_time:.3f}s")
            print(f"   Min Response Time: {min(self.delete_response_times):.3f}s")
            print(f"   Max Response Time: {max(self.delete_response_times):.3f}s")
        
        # Rate limiting analysis
        if self.rate_limit_details:
            print(f"\n🚦 Rate Limiting Analysis:")
            print(f"   Rate Limit Events: {len(self.rate_limit_details)}")
            
            # Show latest rate limit info
            latest = self.rate_limit_details[-1]
            print(f"   Latest Rate Limit Info:")
            print(f"     Retry-After: {latest.get('retry_after', 'Not specified')}")
            print(f"     App Usage: {latest.get('x_app_usage', 'Not specified')}")
            print(f"     Page Usage: {latest.get('x_page_usage', 'Not specified')}")
        
        # Recommendations
        print(f"\n💡 DELETE Rate Limit Recommendations:")
        
        rate_limit_percentage = self.delete_stats['rate_limited'] / total * 100
        
        if rate_limit_percentage == 0:
            print(f"   ✅ No rate limiting detected for DELETE operations")
            print(f"   ✅ Current DELETE rate is sustainable")
        elif rate_limit_percentage < 10:
            print(f"   ⚠️ Minimal DELETE rate limiting ({rate_limit_percentage:.1f}%)")
            print(f"   💡 Consider adding 1-2 second delays between deletes")
        elif rate_limit_percentage < 30:
            print(f"   ⚠️ Moderate DELETE rate limiting ({rate_limit_percentage:.1f}%)")
            print(f"   💡 Add 2-3 second delays between deletes")
        else:
            print(f"   🚨 Heavy DELETE rate limiting ({rate_limit_percentage:.1f}%)")
            print(f"   💡 Add 5+ second delays between deletes")
        
        success_rate = self.delete_stats['successful_deletes'] / total * 100
        if success_rate < 80:
            print(f"   ⚠️ Low DELETE success rate ({success_rate:.1f}%)")
            print(f"   💡 Check permissions and token scope")

def main():
    """Main test function"""
    print("🧪 Facebook DELETE Operations Rate Limit Tester")
    print("=" * 60)
    
    try:
        tester = DeleteLimitTester()
        
        print("⚠️ WARNING: This test will create and delete test comments")
        print("⚠️ Make sure you have permission to post and delete comments")
        
        confirm = input("\nProceed with DELETE testing? (y/n): ").strip().lower()
        if confirm != 'y':
            print("❌ Test cancelled by user")
            return
        
        # Test 1: Basic DELETE rate limits
        tester.test_delete_rate_limits(max_deletes=8, delay_between=1.0)
        
        print("\n" + "="*60)
        
        # Test 2: Different DELETE rates
        advanced = input("\nRun advanced rate testing? (y/n): ").strip().lower()
        if advanced == 'y':
            tester.test_different_delete_rates()
        
        # Final summary
        tester.print_delete_results()
        
    except KeyboardInterrupt:
        print("\n⏹️ Test interrupted by user")
    except Exception as e:
        print(f"\n❌ Test failed: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
