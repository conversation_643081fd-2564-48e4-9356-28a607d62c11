**BAB IV**

**PENUTUP**


**4.1 Kesimpulan**

Berdasarkan serangkaian proses metodologis yang mencakup analisis keb<PERSON><PERSON><PERSON>, perancangan sistem, implementasi model, hingga evaluasi performa, penelitian ini telah berhasil mencapai seluruh tujuan yang telah dirumuskan. Pencapaian tersebut dapat dirincikan sebagai berikut:

1.  [cite_start]Implementasi model *Natural Language Processing* (NLP) IndoBERT untuk analisis dan klasifikasi teks komentar spam judi online telah berhasil direalisasikan[cite: 2741]. [cite_start]Model `BertForSequenceClassification` yang telah dilatih pada korpus bahasa Indonesia berskala besar [cite: 1161, 1200] [cite_start]berhasil diadaptasi dan di-*fine-tune* untuk tugas klasifikasi biner, yakni membedakan komentar "Spam" dan "Bukan Spam"[cite: 1201]. [cite_start]Keberhasilan ini mengonfirmasi kapabilitas IndoBERT sebagai arsitektur yang efektif untuk tugas klasifikasi teks spesifik dalam domain bahasa Indonesia[cite: 1162].

2.  [cite_start]Evaluasi kuantitatif terhadap performa model deteksi menunjukkan tingkat akurasi yang sangat tinggi[cite: 2742]. [cite_start]Pengujian pada *validation set* menghasilkan skor **Akurasi sebesar 0.9962** dan **F1-Score sebesar 0.9962**[cite: 1330]. [cite_start]Nilai *validation loss* yang rendah, yaitu 0.0091, juga mengindikasikan kemampuan generalisasi model yang baik pada data yang belum pernah dilihat sebelumnya[cite: 1329]. [cite_start]Metrik-metrik ini secara kolektif menegaskan bahwa model yang dikembangkan memiliki efektivitas dan reliabilitas tinggi dalam mengidentifikasi komentar spam judol[cite: 1331].

3.  [cite_start]Penyajian hasil deteksi spam telah berhasil diwujudkan dalam bentuk aplikasi web interaktif yang fungsional[cite: 2743]. [cite_start]Dengan memanfaatkan *framework* Streamlit, sebuah sistem dengan antarmuka pengguna grafis dikembangkan untuk memvisualisasikan data dan hasil analisis[cite: 903]. [cite_start]Sistem ini terintegrasi secara penuh dengan Facebook Graph API, memungkinkannya untuk melakukan akuisisi data komentar dan eksekusi tindakan moderasi secara terprogram[cite: 2733, 2734]. [cite_start]Aplikasi yang telah di-*deploy* menyediakan fungsionalitas komprehensif, mulai dari dasbor monitoring, analisis manual, hingga konfigurasi sistem, yang secara efektif mendukung proses moderasi konten[cite: 2210, 2532].

**4.2 Saran**

Meskipun tujuan penelitian telah tercapai, terdapat beberapa area potensial untuk penyempurnaan dan pengembangan lebih lanjut. Berdasarkan limitasi dan temuan selama penelitian, berikut adalah beberapa saran yang diajukan:

1.  [cite_start]**Ekstensifikasi Domain Spam:** Penelitian ini memiliki batasan pada deteksi spam kategori judi online ("judol")[cite: 2731]. Penelitian selanjutnya dapat memperluas cakupan dengan melatih model menggunakan dataset yang lebih heterogen untuk mengidentifikasi berbagai jenis konten tidak diinginkan lainnya, seperti penipuan berkedok tautan (*phishing*), ujaran kebencian, dan disinformasi.

2.  [cite_start]**Implementasi *Active Learning Loop*:** Sistem yang ada saat ini belum memiliki mekanisme umpan balik otomatis dari moderator ke model[cite: 341, 2737]. Disarankan untuk mengimplementasikan paradigma *active learning*, di mana koreksi atau verifikasi yang dilakukan oleh moderator (misalnya, pada halaman "Pending Spam") dapat secara periodik digunakan untuk melatih ulang (*retraining*) model. Pendekatan ini berpotensi meningkatkan akurasi dan adaptabilitas model secara kontinu terhadap evolusi taktik spam.

3.  [cite_start]**Peningkatan Aspek *Interpretability*:** Model *deep learning* seperti IndoBERT seringkali beroperasi sebagai "kotak hitam" (*black box*), yang menyulitkan pemahaman terhadap proses pengambilan keputusannya[cite: 335]. Untuk meningkatkan transparansi dan kepercayaan pengguna, penelitian selanjutnya dapat mengintegrasikan teknik *interpretabilitas* AI, seperti LIME (*Local Interpretable Model-agnostic Explanations*) atau SHAP (*SHapley Additive exPlanations*), guna memvisualisasikan faktor-faktor linguistik yang paling berpengaruh dalam sebuah klasifikasi.

4.  **Pengembangan Metodologi Evaluasi Jangka Panjang:** Disarankan untuk merancang dan mengimplementasikan kerangka kerja evaluasi jangka panjang. Hal ini dapat mencakup pengujian performa secara periodik untuk mengukur degradasi model (*model drift*) seiring waktu dan membandingkan efektivitas berbagai versi model dalam lingkungan produksi yang dinamis.