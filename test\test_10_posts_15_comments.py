#!/usr/bin/env python3
"""
Test Scanning: 10 Posts × 15 Comments Each = 150 Total Comments
Mensimulasikan skenario real dengan 10 postingan dan 15 komentar per postingan
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import time
from datetime import datetime, timedelta
from src.app.streamlit_monitor import AutoMonitor

class MockFacebookAPI:
    """Mock Facebook API dengan 10 posts × 15 comments each"""
    
    def __init__(self):
        # Generate 10 posts
        self.posts = []
        for i in range(1, 11):
            self.posts.append({
                'id': f'post_{i}',
                'message': f'Test post {i} - This is a sample post content',
                'created_time': f'2024-01-{i:02d}T10:00:00Z'
            })
        
        # Generate 15 comments per post (mix of normal and spam)
        self.comments_data = {}
        spam_patterns = [
            "Link daftar situs slot gacor ada di bio!",
            "Menangkan jackpot progresif setiap hari!",
            "Bonus new member 200%, klaim sekarang!",
            "RTP live update setiap menit!",
            "Gabung channel Telegram untuk bocoran pola!",
            "Promo new member 200%, langsung klaim!",
            "Modal kecil untung besar, daftar VIP!",
            "Togel online diskon terbesar!",
            "MAU PENGHASILAN TAMBAHAN? KLIK LINK BIO!",
            "RAHASIA KULIT PUTIH DALAM 7 HARI!",
            "JASA TAMBAH FOLLOWERS MURAH AMAN!",
            "Butuh dana cepat? Proses 5 menit cair!",
            "INVESTASI CRYPTO PASTI UNTUNG 1000%!",
            "MAU DIET TANPA NYIKSA? SOLUSINYA DI BIO!",
            "VIDEO VIRAL TERBARU NO SENSOR!"
        ]
        
        normal_comments = [
            "Terima kasih infonya sangat bermanfaat",
            "Setuju banget dengan pendapat ini",
            "Wah menarik sekali pembahasannya",
            "Boleh tanya lebih detail ga?",
            "Mantap jiwa postingannya",
            "Thanks for sharing this information",
            "Sangat membantu sekali",
            "Ditunggu update selanjutnya",
            "Good point, saya sependapat",
            "Informatif banget nih",
            "Keren banget penjelasannya",
            "Bisa dijelaskan lebih lanjut?",
            "Saya juga pernah mengalami hal serupa",
            "Bagus nih kontennya",
            "Semoga bermanfaat untuk semua"
        ]
        
        for post_id in [p['id'] for p in self.posts]:
            comments = []
            for i in range(1, 16):  # 15 comments per post
                # Mix spam and normal comments (30% spam, 70% normal)
                if i % 3 == 0 or i % 7 == 0:  # Comments 3,6,7,9,12,14,15 are spam
                    message = spam_patterns[i % len(spam_patterns)]
                    is_spam = True
                else:
                    message = normal_comments[i % len(normal_comments)]
                    is_spam = False
                
                comments.append({
                    'id': f'{post_id}_comment_{i}',
                    'message': message,
                    'from': {'name': f'User{i}'},
                    'created_time': f'2024-01-01T{10 + i:02d}:00:00Z',
                    'is_spam_actual': is_spam  # For testing verification
                })
            
            # Sort by creation time (newest first, like Facebook API)
            comments.sort(key=lambda x: x['created_time'], reverse=True)
            self.comments_data[post_id] = comments
    
    def get_recent_posts(self, limit=5):
        """Return recent posts (newest first)"""
        return self.posts[:limit]
    
    def get_post_comments(self, post_id, limit=50):
        """Return comments for a post (newest first)"""
        comments = self.comments_data.get(post_id, [])
        return comments[:limit]
    
    def delete_comment(self, comment_id):
        """Mock delete comment"""
        print(f"🗑️ DELETED: {comment_id}")
        return True

class MockSpamDetector:
    """Mock spam detector with realistic detection"""
    
    def predict(self, text):
        # Spam keywords for detection
        spam_keywords = [
            'slot', 'gacor', 'jackpot', 'bonus', 'rtp', 'telegram', 'bocoran',
            'promo', 'modal', 'untung', 'togel', 'penghasilan', 'link bio',
            'kulit putih', 'followers', 'dana cepat', 'crypto', 'untung',
            'diet', 'video viral', 'no sensor', 'klaim', 'daftar'
        ]
        
        text_lower = text.lower()
        spam_score = sum(1 for keyword in spam_keywords if keyword in text_lower)
        
        # Calculate confidence based on spam keywords found
        if spam_score >= 2:
            confidence = min(0.95, 0.7 + (spam_score * 0.1))
            is_spam = True
        elif spam_score == 1:
            confidence = 0.6
            is_spam = True
        else:
            confidence = 0.1
            is_spam = False
        
        return {
            'is_spam': is_spam,
            'confidence': confidence,
            'label': 'spam' if is_spam else 'normal'
        }

def test_10_posts_15_comments():
    """Test scanning 10 posts with 15 comments each"""
    print("🧪 Testing: 10 Posts × 15 Comments = 150 Total Comments")
    print("=" * 60)
    
    # Setup mock components
    facebook_api = MockFacebookAPI()
    spam_detector = MockSpamDetector()
    
    # Create auto monitor
    auto_monitor = AutoMonitor(
        facebook_api=facebook_api,
        spam_detector=spam_detector,
        poll_interval=30
    )
    
    print("📋 Test Setup:")
    print(f"  Total Posts: {len(facebook_api.posts)}")
    total_comments = sum(len(comments) for comments in facebook_api.comments_data.values())
    print(f"  Total Comments: {total_comments}")
    
    # Count actual spam in test data
    actual_spam_count = 0
    for post_id, comments in facebook_api.comments_data.items():
        for comment in comments:
            if comment.get('is_spam_actual', False):
                actual_spam_count += 1
    print(f"  Actual Spam Comments: {actual_spam_count}")
    
    print(f"\n📊 Current Monitor Configuration:")
    config = auto_monitor.get_config()
    for key, value in config.items():
        print(f"  {key}: {value}")
    
    print(f"\n🔄 Testing Regular Scan (Current Settings)...")
    print("-" * 50)
    
    # Test current scanning capability
    start_time = time.time()
    auto_monitor._check_for_new_comments()
    scan_time = time.time() - start_time
    
    stats = auto_monitor.statistics
    processed_count = len(auto_monitor.processed_comments)
    
    print(f"📊 Scan Results:")
    print(f"  Scan Time: {scan_time:.2f} seconds")
    print(f"  Comments Processed: {stats['comments_processed']}")
    print(f"  Spam Detected & Removed: {stats['spam_removed']}")
    print(f"  Processed Comments Cache: {processed_count}")
    
    # Calculate coverage
    coverage_percentage = (stats['comments_processed'] / total_comments) * 100
    print(f"  Coverage: {stats['comments_processed']}/{total_comments} ({coverage_percentage:.1f}%)")
    
    # Analyze which posts were scanned
    posts_scanned = []
    for comment_id in auto_monitor.processed_comments:
        post_id = comment_id.split('_comment_')[0]
        if post_id not in posts_scanned:
            posts_scanned.append(post_id)
    
    print(f"  Posts Scanned: {len(posts_scanned)}/10")
    print(f"  Posts Scanned IDs: {posts_scanned}")
    
    # Show missed posts
    all_post_ids = [p['id'] for p in facebook_api.posts]
    missed_posts = [pid for pid in all_post_ids if pid not in posts_scanned]
    if missed_posts:
        print(f"  ⚠️ Missed Posts: {missed_posts}")
        missed_comments = sum(len(facebook_api.comments_data[pid]) for pid in missed_posts)
        print(f"  ⚠️ Missed Comments: {missed_comments}")
    
    print(f"\n🎯 Analysis:")
    print("-" * 30)
    
    if coverage_percentage >= 100:
        print("✅ EXCELLENT: All comments scanned")
    elif coverage_percentage >= 80:
        print("✅ GOOD: Most comments scanned")
    elif coverage_percentage >= 60:
        print("⚠️ FAIR: Partial coverage, some comments missed")
    else:
        print("❌ POOR: Many comments missed")
    
    # Recommendations
    print(f"\n💡 Recommendations:")
    print("-" * 30)
    
    if len(posts_scanned) < 10:
        print("📈 To scan all 10 posts:")
        print("   Option 1: Increase posts limit from 5 to 10")
        print("   Option 2: Decrease poll interval from 30s to 15s")
        print("   Option 3: Run multiple scan cycles")
    
    if coverage_percentage < 100:
        print("📈 To improve coverage:")
        print(f"   Current: {stats['comments_processed']}/{total_comments} comments")
        print("   Increase comments per post limit if needed")
    
    # Test multiple scan cycles
    print(f"\n🔄 Testing Multiple Scan Cycles...")
    print("-" * 40)
    
    initial_processed = stats['comments_processed']
    initial_spam_removed = stats['spam_removed']
    
    # Run 2 more scan cycles
    for cycle in range(2, 4):
        print(f"Cycle {cycle}:")
        auto_monitor._check_for_new_comments()
        new_processed = auto_monitor.statistics['comments_processed'] - initial_processed
        new_spam = auto_monitor.statistics['spam_removed'] - initial_spam_removed
        print(f"  New comments processed: {new_processed}")
        print(f"  New spam removed: {new_spam}")
        
        if new_processed == 0:
            print("  ✅ No new comments (all already processed)")
            break
    
    final_stats = auto_monitor.statistics
    final_coverage = (final_stats['comments_processed'] / total_comments) * 100
    
    print(f"\n📈 Final Results After Multiple Cycles:")
    print(f"  Total Comments Processed: {final_stats['comments_processed']}/{total_comments}")
    print(f"  Total Spam Removed: {final_stats['spam_removed']}")
    print(f"  Final Coverage: {final_coverage:.1f}%")
    
    print(f"\n✅ Test Completed!")
    print("🎯 Key Findings:")
    print(f"  • Current settings can process {stats['comments_processed']} comments per scan")
    print(f"  • Coverage with default settings: {coverage_percentage:.1f}%")
    print(f"  • Posts scanned per cycle: {len(posts_scanned)}/10")
    print(f"  • Spam detection and removal: Working ✅")
    
    return {
        'total_comments': total_comments,
        'processed_comments': final_stats['comments_processed'],
        'spam_removed': final_stats['spam_removed'],
        'coverage_percentage': final_coverage,
        'posts_scanned': len(posts_scanned),
        'scan_time': scan_time
    }

if __name__ == "__main__":
    test_10_posts_15_comments()
