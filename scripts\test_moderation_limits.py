#!/usr/bin/env python3
"""
Facebook Comment Moderation Limits Tester
Khusus untuk testing limits moderasi komentar dan deletion
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import time
import requests
import json
import threading
from datetime import datetime, timedelta
from dotenv import load_dotenv
from concurrent.futures import ThreadPoolExecutor, as_completed

class ModerationLimitTester:
    """Test limits untuk moderasi komentar Facebook"""
    
    def __init__(self):
        """Initialize moderation tester"""
        load_dotenv()
        
        self.page_id = os.getenv('PAGE_ID')
        self.access_token = os.getenv('PAGE_ACCESS_TOKEN')
        self.base_url = "https://graph.facebook.com/v18.0"
        
        # Thread-safe counters
        self.lock = threading.Lock()
        self.stats = {
            'total_requests': 0,
            'successful_reads': 0,
            'failed_reads': 0,
            'successful_deletes': 0,
            'failed_deletes': 0,
            'rate_limits': 0,
            'permission_errors': 0,
            'network_errors': 0
        }
        
        self.response_times = []
        self.error_details = []
        
        if not self.page_id or not self.access_token:
            raise ValueError("PAGE_ID dan PAGE_ACCESS_TOKEN harus diset di .env file")
    
    def update_stats(self, stat_key, increment=1):
        """Thread-safe stats update"""
        with self.lock:
            self.stats[stat_key] += increment
    
    def add_response_time(self, response_time):
        """Thread-safe response time tracking"""
        with self.lock:
            self.response_times.append(response_time)
    
    def add_error_detail(self, error_detail):
        """Thread-safe error detail tracking"""
        with self.lock:
            self.error_details.append(error_detail)
    
    def test_single_comment_read(self, comment_id, thread_id=0):
        """Test reading a single comment"""
        start_time = time.time()
        
        try:
            url = f"{self.base_url}/{comment_id}"
            params = {
                'access_token': self.access_token,
                'fields': 'id,message,from,created_time'
            }
            
            response = requests.get(url, params=params, timeout=10)
            response_time = time.time() - start_time
            
            self.update_stats('total_requests')
            self.add_response_time(response_time)
            
            if response.status_code == 200:
                self.update_stats('successful_reads')
                return True, response.json(), response_time
            
            elif response.status_code == 429:
                self.update_stats('rate_limits')
                self.update_stats('failed_reads')
                
                self.add_error_detail({
                    'thread_id': thread_id,
                    'comment_id': comment_id,
                    'error_type': 'rate_limit',
                    'status_code': 429,
                    'response_time': response_time,
                    'retry_after': response.headers.get('Retry-After'),
                    'timestamp': datetime.now().isoformat()
                })
                
                return False, 'Rate Limited', response_time
            
            else:
                self.update_stats('failed_reads')
                
                try:
                    error_data = response.json()
                except:
                    error_data = response.text
                
                self.add_error_detail({
                    'thread_id': thread_id,
                    'comment_id': comment_id,
                    'error_type': 'api_error',
                    'status_code': response.status_code,
                    'error_data': error_data,
                    'response_time': response_time,
                    'timestamp': datetime.now().isoformat()
                })
                
                return False, error_data, response_time
        
        except Exception as e:
            response_time = time.time() - start_time
            self.update_stats('total_requests')
            self.update_stats('network_errors')
            self.update_stats('failed_reads')
            self.add_response_time(response_time)
            
            self.add_error_detail({
                'thread_id': thread_id,
                'comment_id': comment_id,
                'error_type': 'network_error',
                'error': str(e),
                'response_time': response_time,
                'timestamp': datetime.now().isoformat()
            })
            
            return False, str(e), response_time
    
    def test_single_comment_delete_check(self, comment_id, thread_id=0):
        """Test delete permission check (tidak benar-benar delete)"""
        start_time = time.time()
        
        try:
            # Use HEAD request to check if we can access the comment for deletion
            url = f"{self.base_url}/{comment_id}"
            params = {'access_token': self.access_token}
            
            # First check if comment exists and we have access
            response = requests.get(url, params=params, timeout=10)
            response_time = time.time() - start_time
            
            self.update_stats('total_requests')
            self.add_response_time(response_time)
            
            if response.status_code == 200:
                # Comment exists and we can read it
                # Now check if we would be able to delete it (simulate only)
                self.update_stats('successful_deletes')
                return True, 'Delete permission available', response_time
            
            elif response.status_code == 403:
                self.update_stats('permission_errors')
                self.update_stats('failed_deletes')
                
                self.add_error_detail({
                    'thread_id': thread_id,
                    'comment_id': comment_id,
                    'error_type': 'permission_error',
                    'status_code': 403,
                    'response_time': response_time,
                    'timestamp': datetime.now().isoformat()
                })
                
                return False, 'Permission Denied', response_time
            
            elif response.status_code == 429:
                self.update_stats('rate_limits')
                self.update_stats('failed_deletes')
                
                return False, 'Rate Limited', response_time
            
            else:
                self.update_stats('failed_deletes')
                return False, f'Error {response.status_code}', response_time
        
        except Exception as e:
            response_time = time.time() - start_time
            self.update_stats('total_requests')
            self.update_stats('network_errors')
            self.update_stats('failed_deletes')
            self.add_response_time(response_time)
            
            return False, str(e), response_time
    
    def get_test_comments(self, max_comments=50):
        """Get comments untuk testing"""
        print("📥 Getting test comments...")
        
        try:
            # Get posts first
            posts_url = f"{self.base_url}/{self.page_id}/posts"
            params = {
                'access_token': self.access_token,
                'limit': 10,
                'fields': 'id'
            }
            
            response = requests.get(posts_url, params=params, timeout=30)
            if response.status_code != 200:
                print(f"❌ Failed to get posts: {response.status_code}")
                return []
            
            posts = response.json().get('data', [])
            print(f"✅ Found {len(posts)} posts")
            
            # Get comments from posts
            all_comments = []
            for post in posts:
                if len(all_comments) >= max_comments:
                    break
                
                comments_url = f"{self.base_url}/{post['id']}/comments"
                params = {
                    'access_token': self.access_token,
                    'limit': min(25, max_comments - len(all_comments)),
                    'fields': 'id,message,from'
                }
                
                response = requests.get(comments_url, params=params, timeout=30)
                if response.status_code == 200:
                    comments = response.json().get('data', [])
                    all_comments.extend(comments)
                    print(f"   Post {post['id']}: {len(comments)} comments")
            
            print(f"✅ Total comments collected: {len(all_comments)}")
            return all_comments
        
        except Exception as e:
            print(f"❌ Error getting test comments: {str(e)}")
            return []
    
    def test_concurrent_reads(self, comments, max_workers=5, duration_seconds=60):
        """Test concurrent comment reads"""
        print(f"\n🔄 Testing Concurrent Reads")
        print(f"Workers: {max_workers}, Duration: {duration_seconds}s")
        print("=" * 50)
        
        if not comments:
            print("❌ No comments available for testing")
            return
        
        start_time = time.time()
        end_time = start_time + duration_seconds
        
        def worker_task(thread_id):
            """Worker task untuk concurrent testing"""
            request_count = 0
            
            while time.time() < end_time:
                # Pick random comment
                comment = comments[request_count % len(comments)]
                comment_id = comment['id']
                
                success, result, response_time = self.test_single_comment_read(comment_id, thread_id)
                request_count += 1
                
                # Small delay to prevent overwhelming
                time.sleep(0.1)
            
            return request_count
        
        # Run concurrent workers
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            futures = [executor.submit(worker_task, i) for i in range(max_workers)]
            
            # Monitor progress
            while time.time() < end_time:
                time.sleep(5)
                elapsed = time.time() - start_time
                with self.lock:
                    current_rps = self.stats['total_requests'] / elapsed if elapsed > 0 else 0
                    print(f"   Progress: {self.stats['total_requests']} requests, "
                          f"{current_rps:.1f} RPS, "
                          f"{self.stats['successful_reads']} success, "
                          f"{self.stats['rate_limits']} rate limits")
            
            # Wait for completion
            total_worker_requests = sum(future.result() for future in as_completed(futures))
        
        total_time = time.time() - start_time
        print(f"\n📊 Concurrent Read Results:")
        print(f"   Total Time: {total_time:.2f}s")
        print(f"   Worker Requests: {total_worker_requests}")
        print(f"   Actual RPS: {self.stats['total_requests']/total_time:.2f}")
    
    def test_moderation_workflow(self, comments, max_operations=30):
        """Test realistic moderation workflow"""
        print(f"\n🛡️ Testing Moderation Workflow")
        print(f"Operations: {max_operations}")
        print("=" * 50)
        
        if not comments:
            print("❌ No comments available for testing")
            return
        
        workflow_stats = {
            'comments_processed': 0,
            'spam_detected': 0,
            'delete_checks': 0,
            'successful_operations': 0
        }
        
        for i, comment in enumerate(comments[:max_operations]):
            comment_id = comment['id']
            message = comment.get('message', 'No message')[:50]
            
            print(f"{i+1}. Processing {comment_id}...")
            
            # Step 1: Read comment (simulate spam detection)
            success, data, read_time = self.test_single_comment_read(comment_id, 0)
            workflow_stats['comments_processed'] += 1
            
            if success:
                print(f"   ✅ Read successful ({read_time:.3f}s)")
                
                # Simulate spam detection
                is_spam = 'spam' in message.lower() or len(message) > 100
                if is_spam:
                    workflow_stats['spam_detected'] += 1
                    print(f"   🚨 Spam detected: {message}...")
                    
                    # Step 2: Check delete permission
                    success, result, delete_time = self.test_single_comment_delete_check(comment_id, 0)
                    workflow_stats['delete_checks'] += 1
                    
                    if success:
                        workflow_stats['successful_operations'] += 1
                        print(f"   ✅ Delete check passed ({delete_time:.3f}s)")
                    else:
                        print(f"   ❌ Delete check failed: {result} ({delete_time:.3f}s)")
                else:
                    workflow_stats['successful_operations'] += 1
                    print(f"   ✅ Normal comment, no action needed")
            else:
                print(f"   ❌ Read failed: {data} ({read_time:.3f}s)")
            
            # Delay between operations
            time.sleep(1)
        
        print(f"\n📊 Moderation Workflow Results:")
        for key, value in workflow_stats.items():
            print(f"   {key.replace('_', ' ').title()}: {value}")
    
    def generate_final_report(self):
        """Generate comprehensive test report"""
        print(f"\n📋 MODERATION LIMITS TEST REPORT")
        print("=" * 60)
        
        with self.lock:
            total_requests = self.stats['total_requests']
            success_rate = ((self.stats['successful_reads'] + self.stats['successful_deletes']) / 
                          total_requests * 100) if total_requests > 0 else 0
            
            print(f"📊 Request Statistics:")
            print(f"   Total Requests: {total_requests}")
            print(f"   Successful Reads: {self.stats['successful_reads']}")
            print(f"   Failed Reads: {self.stats['failed_reads']}")
            print(f"   Successful Delete Checks: {self.stats['successful_deletes']}")
            print(f"   Failed Delete Checks: {self.stats['failed_deletes']}")
            print(f"   Overall Success Rate: {success_rate:.1f}%")
            
            print(f"\n❌ Error Breakdown:")
            print(f"   Rate Limits: {self.stats['rate_limits']}")
            print(f"   Permission Errors: {self.stats['permission_errors']}")
            print(f"   Network Errors: {self.stats['network_errors']}")
            
            if self.response_times:
                avg_response_time = sum(self.response_times) / len(self.response_times)
                print(f"\n⏱️ Performance:")
                print(f"   Average Response Time: {avg_response_time:.3f}s")
                print(f"   Min Response Time: {min(self.response_times):.3f}s")
                print(f"   Max Response Time: {max(self.response_times):.3f}s")
            
            # Rate limiting analysis
            rate_limit_percentage = (self.stats['rate_limits'] / total_requests * 100) if total_requests > 0 else 0
            
            print(f"\n🚦 Rate Limiting Analysis:")
            print(f"   Rate Limit Hits: {self.stats['rate_limits']}")
            print(f"   Rate Limit Percentage: {rate_limit_percentage:.1f}%")
            
            if rate_limit_percentage == 0:
                print(f"   ✅ No rate limiting - safe to increase request rate")
            elif rate_limit_percentage < 5:
                print(f"   ⚠️ Minimal rate limiting - current rate is near optimal")
            elif rate_limit_percentage < 20:
                print(f"   ⚠️ Moderate rate limiting - consider reducing request rate")
            else:
                print(f"   🚨 Heavy rate limiting - significantly reduce request rate")
            
            print(f"\n💡 Recommendations for Comment Moderation:")
            if self.stats['rate_limits'] == 0:
                print(f"   ✅ Current request pattern is sustainable")
                print(f"   ✅ Can handle moderate comment volumes")
            else:
                print(f"   ⚠️ Add delays between moderation operations")
                print(f"   ⚠️ Consider batch processing with rate limiting")
            
            if self.stats['permission_errors'] > 0:
                print(f"   🔑 Check Facebook page permissions and token scope")
            
            if avg_response_time > 1.0:
                print(f"   ⏱️ High response times - consider timeout handling")

def main():
    """Main test function"""
    print("🧪 Facebook Comment Moderation Limits Tester")
    print("=" * 60)
    
    try:
        tester = ModerationLimitTester()
        
        # Get test comments
        comments = tester.get_test_comments(max_comments=30)
        
        if not comments:
            print("❌ No comments available for testing")
            return
        
        # Test 1: Moderation workflow
        tester.test_moderation_workflow(comments, max_operations=15)
        
        # Test 2: Concurrent reads
        print(f"\n⚠️ Starting concurrent test in 3 seconds...")
        time.sleep(3)
        tester.test_concurrent_reads(comments, max_workers=3, duration_seconds=30)
        
        # Generate report
        tester.generate_final_report()
        
    except KeyboardInterrupt:
        print("\n⏹️ Test interrupted by user")
        tester.generate_final_report()
    except Exception as e:
        print(f"\n❌ Test failed: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
