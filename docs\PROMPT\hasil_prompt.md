




# Black Box Testing Report for Judol Remover

Be<PERSON><PERSON> adalah hasil pengujian black box untuk fitur-fitur utama pada aplikasi Judol Remover.

| Nama Test | Langkah | Expected Result |
| :--- | :--- | :--- |
| **Dashboard - Menampilkan Statistik Awal** | 1. Buka aplikasi. <br> 2. Amati bagian statistik di dashboard. | 1. <PERSON><PERSON><PERSON>rik (Comments Processed, Spam Detected, Spam Removed, Pending Review) menampilkan angka 0. <br> 2. Status Monitor menampilkan "🔴 Stopped". <br> 3. Status Auto Delete menampilkan "🔴 Disabled" atau "🟢 Enabled" sesuai konfigurasi default. |
| **Dashboard - Refresh <PERSON>ingan** | 1. Buka aplikasi dan pastikan API Facebook terhubung. <br> 2. Navigasi ke dashboard. <br> 3. <PERSON><PERSON> tombol "🔄 Refresh Posts". | 1. Aplikasi menampilkan spinner atau pesan loading. <br> 2. Daftar postingan dan komentar di bawahnya diperbarui dengan data terbaru dari Facebook. <br> 3. Tidak ada error yang muncul. |
| **Dashboard - Deteksi Spam Manual pada Komentar** | 1. Buka salah satu postingan di dashboard untuk melihat komentarnya. <br> 2. Temukan komentar yang diduga spam. <br> 3. Klik tombol "🗑️ Delete" pada komentar tersebut. | 1. Komentar tersebut berhasil dihapus dari tampilan. <br> 2. Notifikasi "Deleted comment..." muncul. <br> 3. Metrik "Spam Removed" di statistik bertambah 1. |
| **Manual Check - Menjalankan Pengecekan** | 1. Navigasi ke halaman "Manual Check". <br> 2. Pilih salah satu postingan dari dropdown. <br> 3. Klik tombol "🔍 Check Post". | 1. Aplikasi menampilkan spinner "Checking post for spam...". <br> 2. Hasil pengecekan ditampilkan di bawahnya (Comments Checked, Spam Found, Spam Removed, Errors). <br> 3. Detail komentar yang diperiksa muncul di bagian "Detailed Results". |
| **Manual Check - Hasil Pengecekan dengan Auto-Delete Aktif** | 1. Pastikan "Auto Delete Spam" aktif di Settings. <br> 2. Jalankan "Manual Check" pada postingan yang memiliki komentar spam. | 1. Metrik "Spam Found" dan "Spam Removed" pada hasil pengecekan menampilkan jumlah yang sama. <br> 2. Pada "Detailed Results", komentar spam ditandai dengan status "DELETED". |
| **Manual Check - Hasil Pengecekan dengan Auto-Delete Nonaktif** | 1. Pastikan "Auto Delete Spam" nonaktif di Settings. <br> 2. Jalankan "Manual Check" pada postingan yang memiliki komentar spam. | 1. Metrik "Spam Found" menampilkan jumlah spam yang terdeteksi, tetapi "Spam Removed" menampilkan 0. <br> 2. Komentar spam yang terdeteksi ditambahkan ke halaman "Pending Spam". |
| **Pending Spam - Menampilkan Komentar Spam** | 1. Nonaktifkan "Auto Delete" di Settings. <br> 2. Jalankan "Manual Check" pada postingan yang mengandung spam. <br> 3. Navigasi ke halaman "Pending Spam". | 1. Halaman "Pending Spam" menampilkan daftar komentar yang terdeteksi sebagai spam dari hasil "Manual Check". <br> 2. Setiap item menampilkan informasi penulis, isi pesan, dan confidence score. |
| **Pending Spam - Hapus Satu Komentar** | 1. Buka halaman "Pending Spam" yang berisi beberapa komentar. <br> 2. Klik tombol "🗑️ Delete" pada salah satu komentar. | 1. Komentar tersebut hilang dari daftar. <br> 2. Notifikasi "Comment deleted" muncul. <br> 3. Metrik "Spam Removed" di dashboard bertambah 1. |
| **Pending Spam - Hapus Semua Komentar** | 1. Buka halaman "Pending Spam" yang berisi beberapa komentar. <br> 2. Klik tombol "🗑️ Delete All Spam". | 1. Semua komentar di halaman tersebut hilang. <br> 2. Notifikasi "Deleted X spam comments" muncul. <br> 3. Metrik "Spam Removed" di dashboard bertambah sesuai jumlah komentar yang dihapus. |
| **Pending Spam - Tandai Semua Sebagai Normal** | 1. Buka halaman "Pending Spam" yang berisi beberapa komentar. <br> 2. Klik tombol "✅ Mark All as Normal". | 1. Semua komentar di halaman tersebut hilang. <br> 2. Notifikasi "All comments marked as normal" muncul. <br> 3. Metrik "Spam Removed" di dashboard tidak berubah. |
| **Settings - Update Konfigurasi Facebook API (Sukses)** | 1. Navigasi ke halaman "Settings". <br> 2. Masukkan Page ID dan Page Access Token yang valid. <br> 3. Klik "💾 Update Facebook Settings". | 1. Notifikasi "Facebook API settings updated successfully!" muncul. <br> 2. Status Facebook API di "System Information" berubah menjadi "🟢 Connected". |
| **Settings - Update Konfigurasi Facebook API (Gagal)** | 1. Navigasi ke halaman "Settings". <br> 2. Masukkan Page ID atau Page Access Token yang tidak valid. <br> 3. Klik "💾 Update Facebook Settings". | 1. Notifikasi error "Failed to update Facebook settings..." muncul. <br> 2. Status Facebook API di "System Information" tetap "🔴 Not Connected". |
| **Settings - Update Confidence Threshold** | 1. Navigasi ke halaman "Settings". <br> 2. Ubah nilai slider "Confidence Threshold". <br> 3. Klik "💾 Update Detection Settings". | 1. Notifikasi "Detection settings updated!" muncul. <br> 2. Nilai threshold yang baru akan digunakan untuk deteksi spam selanjutnya. |
| **Settings - Mengaktifkan/Menonaktifkan Auto Delete** | 1. Navigasi ke halaman "Settings". <br> 2. Centang atau hapus centang pada "Auto Delete Spam". <br> 3. Klik "💾 Update Monitor Settings". | 1. Notifikasi "Monitor settings updated!" muncul. <br> 2. Perilaku penghapusan spam (otomatis atau manual via "Pending Spam") berubah sesuai pengaturan baru. |
| **Auto-Monitor - Status Berjalan** | 1. Di sidebar, klik tombol "Start Monitor". | 1. Tombol berubah menjadi "Stop Monitor". <br> 2. Di dashboard, status monitor berubah menjadi "🟢 Running". <br> 3. Status auto-refresh di pojok kanan atas dashboard menampilkan "🔄 Auto Refresh ON". |
| **Auto-Monitor - Status Berhenti** | 1. Saat monitor sedang berjalan, klik tombol "Stop Monitor" di sidebar. | 1. Tombol berubah menjadi "Start Monitor". <br> 2. Di dashboard, status monitor berubah menjadi "🔴 Stopped". <br> 3. Status auto-refresh di pojok kanan atas dashboard menampilkan "⏹️ Monitor Stopped". |
