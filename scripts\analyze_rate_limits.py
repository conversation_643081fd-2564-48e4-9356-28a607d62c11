#!/usr/bin/env python3
"""
Analyze Facebook Graph API Rate Limits
Analyze rate limit headers dan current usage
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import time
import requests
import json
from datetime import datetime
from dotenv import load_dotenv

class RateLimitAnalyzer:
    """Analyze Facebook Graph API rate limits"""
    
    def __init__(self):
        """Initialize rate limit analyzer"""
        load_dotenv()
        
        self.page_id = os.getenv('PAGE_ID')
        self.access_token = os.getenv('PAGE_ACCESS_TOKEN')
        self.base_url = "https://graph.facebook.com/v18.0"
        
        if not self.page_id or not self.access_token:
            raise ValueError("PAGE_ID dan PAGE_ACCESS_TOKEN harus diset di .env")
    
    def make_request_with_headers(self, method, url, params=None, description="request"):
        """
        Make request dan analyze rate limit headers
        
        Returns:
            dict: Request result dengan rate limit info
        """
        start_time = time.time()
        
        try:
            if method.upper() == 'GET':
                response = requests.get(url, params=params, timeout=10)
            elif method.upper() == 'DELETE':
                response = requests.delete(url, params=params, timeout=10)
            elif method.upper() == 'POST':
                response = requests.post(url, params=params, timeout=10)
            else:
                raise ValueError(f"Unsupported method: {method}")
            
            response_time = time.time() - start_time
            
            # Extract rate limit headers
            rate_limit_headers = {
                'x_app_usage': response.headers.get('X-App-Usage'),
                'x_page_usage': response.headers.get('X-Page-Usage'),
                'x_ad_account_usage': response.headers.get('X-Ad-Account-Usage'),
                'retry_after': response.headers.get('Retry-After'),
                'x_business_use_case_usage': response.headers.get('X-Business-Use-Case-Usage')
            }
            
            # Parse usage data
            parsed_usage = {}
            for header_name, header_value in rate_limit_headers.items():
                if header_value:
                    try:
                        parsed_usage[header_name] = json.loads(header_value)
                    except:
                        parsed_usage[header_name] = header_value
            
            result = {
                'timestamp': datetime.now().isoformat(),
                'description': description,
                'method': method.upper(),
                'url': url,
                'status_code': response.status_code,
                'response_time': response_time,
                'rate_limit_headers': rate_limit_headers,
                'parsed_usage': parsed_usage,
                'success': response.status_code < 400
            }
            
            # Get response data if successful
            if response.status_code < 400:
                try:
                    result['response_data'] = response.json()
                except:
                    result['response_data'] = response.text
            else:
                try:
                    result['error_data'] = response.json()
                except:
                    result['error_data'] = response.text
            
            return result
            
        except Exception as e:
            return {
                'timestamp': datetime.now().isoformat(),
                'description': description,
                'method': method.upper(),
                'url': url,
                'status_code': 0,
                'response_time': time.time() - start_time,
                'error': str(e),
                'success': False
            }
    
    def analyze_current_usage(self):
        """Analyze current API usage"""
        print("📊 Analyzing Current Facebook API Usage")
        print("=" * 50)
        
        # Test different endpoints
        endpoints_to_test = [
            {
                'method': 'GET',
                'url': f"{self.base_url}/{self.page_id}",
                'params': {'access_token': self.access_token, 'fields': 'id,name'},
                'description': 'Page Info'
            },
            {
                'method': 'GET',
                'url': f"{self.base_url}/{self.page_id}/posts",
                'params': {'access_token': self.access_token, 'limit': 5},
                'description': 'Posts List'
            }
        ]
        
        results = []
        
        for endpoint in endpoints_to_test:
            print(f"\n🔍 Testing {endpoint['description']}...")
            
            result = self.make_request_with_headers(
                endpoint['method'],
                endpoint['url'],
                endpoint['params'],
                endpoint['description']
            )
            
            results.append(result)
            
            if result['success']:
                print(f"✅ {endpoint['description']}: OK ({result['response_time']:.3f}s)")
                self.print_rate_limit_info(result)
            else:
                print(f"❌ {endpoint['description']}: Error {result['status_code']} ({result['response_time']:.3f}s)")
                if 'error_data' in result:
                    print(f"   Error: {result['error_data']}")
        
        return results
    
    def print_rate_limit_info(self, result):
        """Print rate limit information dari response"""
        parsed_usage = result.get('parsed_usage', {})
        
        if not parsed_usage:
            print("   ℹ️ No rate limit headers found")
            return
        
        print("   📈 Rate Limit Info:")
        
        # App Usage
        if 'x_app_usage' in parsed_usage:
            app_usage = parsed_usage['x_app_usage']
            print(f"   📱 App Usage:")
            for key, value in app_usage.items():
                print(f"      {key}: {value}%")
        
        # Page Usage
        if 'x_page_usage' in parsed_usage:
            page_usage = parsed_usage['x_page_usage']
            print(f"   📄 Page Usage:")
            for key, value in page_usage.items():
                print(f"      {key}: {value}%")
        
        # Business Use Case Usage
        if 'x_business_use_case_usage' in parsed_usage:
            business_usage = parsed_usage['x_business_use_case_usage']
            print(f"   💼 Business Usage:")
            for key, value in business_usage.items():
                print(f"      {key}: {value}")
        
        # Retry After
        if 'retry_after' in result['rate_limit_headers'] and result['rate_limit_headers']['retry_after']:
            print(f"   ⏰ Retry After: {result['rate_limit_headers']['retry_after']} seconds")
    
    def test_delete_rate_limits_safely(self):
        """Test DELETE rate limits tanpa benar-benar delete"""
        print(f"\n🗑️ Testing DELETE Rate Limits (Safe Mode)")
        print("=" * 50)
        
        # Get some comments first
        posts_result = self.make_request_with_headers(
            'GET',
            f"{self.base_url}/{self.page_id}/posts",
            {'access_token': self.access_token, 'limit': 3},
            'Get Posts for DELETE test'
        )
        
        if not posts_result['success']:
            print("❌ Failed to get posts for DELETE testing")
            return
        
        posts = posts_result['response_data'].get('data', [])
        if not posts:
            print("❌ No posts found for DELETE testing")
            return
        
        # Get comments from first post
        post_id = posts[0]['id']
        comments_result = self.make_request_with_headers(
            'GET',
            f"{self.base_url}/{post_id}/comments",
            {'access_token': self.access_token, 'limit': 5},
            'Get Comments for DELETE test'
        )
        
        if not comments_result['success']:
            print("❌ Failed to get comments for DELETE testing")
            return
        
        comments = comments_result['response_data'].get('data', [])
        if not comments:
            print("❌ No comments found for DELETE testing")
            return
        
        print(f"✅ Found {len(comments)} comments for testing")
        
        # Test DELETE endpoint access (without actually deleting)
        for i, comment in enumerate(comments[:3]):  # Test first 3 comments
            comment_id = comment['id']
            message = comment.get('message', 'No message')[:50]
            
            print(f"\n{i+1}. Testing DELETE access for comment {comment_id}...")
            print(f"   Message: {message}...")
            
            # Use HEAD request to test access without deleting
            try:
                response = requests.head(
                    f"{self.base_url}/{comment_id}",
                    params={'access_token': self.access_token},
                    timeout=10
                )
                
                print(f"   Status: {response.status_code}")
                
                # Check rate limit headers
                rate_limit_headers = {
                    'x_app_usage': response.headers.get('X-App-Usage'),
                    'x_page_usage': response.headers.get('X-Page-Usage'),
                    'retry_after': response.headers.get('Retry-After')
                }
                
                if any(rate_limit_headers.values()):
                    print(f"   📈 Rate Limit Headers:")
                    for key, value in rate_limit_headers.items():
                        if value:
                            print(f"      {key}: {value}")
                
                if response.status_code == 200:
                    print(f"   ✅ DELETE access available")
                elif response.status_code == 403:
                    print(f"   ❌ DELETE access denied")
                elif response.status_code == 429:
                    print(f"   ⚠️ Rate limited")
                else:
                    print(f"   ⚠️ Unexpected status: {response.status_code}")
                
            except Exception as e:
                print(f"   ❌ Error testing DELETE access: {str(e)}")
            
            # Small delay between tests
            time.sleep(1)
    
    def monitor_rate_limits_realtime(self, duration_seconds=60):
        """Monitor rate limits in real-time"""
        print(f"\n📡 Real-time Rate Limit Monitoring")
        print(f"Duration: {duration_seconds} seconds")
        print("=" * 50)
        
        start_time = time.time()
        end_time = start_time + duration_seconds
        
        request_count = 0
        rate_limit_data = []
        
        while time.time() < end_time:
            request_count += 1
            
            # Make a simple request
            result = self.make_request_with_headers(
                'GET',
                f"{self.base_url}/{self.page_id}",
                {'access_token': self.access_token, 'fields': 'id'},
                f'Monitor Request {request_count}'
            )
            
            current_time = time.time() - start_time
            
            if result['success']:
                print(f"⏰ {current_time:6.1f}s: Request {request_count:2d} - OK ({result['response_time']:.3f}s)")
                
                # Extract usage percentages
                parsed_usage = result.get('parsed_usage', {})
                if 'x_app_usage' in parsed_usage:
                    app_usage = parsed_usage['x_app_usage']
                    call_count = app_usage.get('call_count', 0)
                    total_time = app_usage.get('total_time', 0)
                    total_cputime = app_usage.get('total_cputime', 0)
                    
                    print(f"         App Usage - Calls: {call_count}%, Time: {total_time}%, CPU: {total_cputime}%")
                    
                    rate_limit_data.append({
                        'timestamp': current_time,
                        'request_count': request_count,
                        'call_count': call_count,
                        'total_time': total_time,
                        'total_cputime': total_cputime
                    })
            else:
                print(f"⏰ {current_time:6.1f}s: Request {request_count:2d} - ERROR {result['status_code']}")
                
                if result['status_code'] == 429:
                    print(f"         🚨 RATE LIMITED!")
                    break
            
            # Wait before next request
            time.sleep(2)  # 0.5 RPS
        
        # Summary
        print(f"\n📊 Monitoring Summary:")
        print(f"   Total Requests: {request_count}")
        print(f"   Duration: {time.time() - start_time:.1f}s")
        
        if rate_limit_data:
            latest = rate_limit_data[-1]
            print(f"   Final Usage:")
            print(f"     Call Count: {latest['call_count']}%")
            print(f"     Total Time: {latest['total_time']}%")
            print(f"     CPU Time: {latest['total_cputime']}%")

def main():
    """Main function"""
    print("🔍 Facebook Graph API Rate Limit Analyzer")
    print("=" * 60)
    
    try:
        analyzer = RateLimitAnalyzer()
        
        # Test 1: Current usage analysis
        results = analyzer.analyze_current_usage()
        
        # Test 2: DELETE limits (safe mode)
        analyzer.test_delete_rate_limits_safely()
        
        # Test 3: Real-time monitoring
        print("\n" + "="*60)
        monitor = input("\nRun real-time monitoring? (y/n): ").strip().lower()
        
        if monitor == 'y':
            duration = input("Duration in seconds (default 30): ").strip()
            try:
                duration = int(duration) if duration else 30
            except:
                duration = 30
            
            analyzer.monitor_rate_limits_realtime(duration)
        
        print(f"\n💡 Analysis Complete!")
        print("Check the output above for rate limit insights")
        
    except KeyboardInterrupt:
        print("\n⏹️ Analysis interrupted by user")
    except Exception as e:
        print(f"\n❌ Analysis failed: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
