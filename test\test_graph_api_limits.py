#!/usr/bin/env python3
"""
Facebook Graph API Rate Limit Tester
Test maksimal request untuk moderasi komentar Facebook
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import time
import requests
import json
from datetime import datetime, timedelta
from dotenv import load_dotenv
import threading
from collections import defaultdict

class GraphAPILimitTester:
    """Test Facebook Graph API rate limits untuk moderasi komentar"""
    
    def __init__(self):
        """Initialize API tester"""
        load_dotenv()
        
        self.page_id = os.getenv('PAGE_ID')
        self.access_token = os.getenv('PAGE_ACCESS_TOKEN')
        self.base_url = "https://graph.facebook.com/v18.0"
        
        # Rate limit tracking
        self.request_count = 0
        self.error_count = 0
        self.success_count = 0
        self.rate_limit_hits = 0
        self.start_time = None
        
        # Request timing
        self.request_times = []
        self.error_responses = []
        
        # API endpoints to test
        self.endpoints = {
            'posts': f"{self.base_url}/{self.page_id}/posts",
            'comments': f"{self.base_url}/{{post_id}}/comments",
            'delete_comment': f"{self.base_url}/{{comment_id}}",
            'page_info': f"{self.base_url}/{self.page_id}"
        }
        
        if not self.page_id or not self.access_token:
            raise ValueError("PAGE_ID dan PAGE_ACCESS_TOKEN harus diset di .env file")
    
    def make_request(self, method, url, params=None, description="request"):
        """
        Make API request dengan tracking
        
        Args:
            method (str): HTTP method (GET, POST, DELETE)
            url (str): Request URL
            params (dict): Request parameters
            description (str): Request description
            
        Returns:
            tuple: (success, response_data, status_code, response_time)
        """
        start_time = time.time()
        self.request_count += 1
        
        try:
            if method.upper() == 'GET':
                response = requests.get(url, params=params, timeout=30)
            elif method.upper() == 'DELETE':
                response = requests.delete(url, params=params, timeout=30)
            elif method.upper() == 'POST':
                response = requests.post(url, params=params, timeout=30)
            else:
                raise ValueError(f"Unsupported method: {method}")
            
            response_time = time.time() - start_time
            self.request_times.append(response_time)
            
            # Check for rate limiting
            if response.status_code == 429:
                self.rate_limit_hits += 1
                self.error_count += 1
                
                # Parse rate limit headers if available
                rate_limit_info = {
                    'retry_after': response.headers.get('Retry-After'),
                    'x_app_usage': response.headers.get('X-App-Usage'),
                    'x_page_usage': response.headers.get('X-Page-Usage'),
                    'x_ad_account_usage': response.headers.get('X-Ad-Account-Usage')
                }
                
                self.error_responses.append({
                    'timestamp': datetime.now().isoformat(),
                    'description': description,
                    'status_code': response.status_code,
                    'error': 'Rate Limited',
                    'rate_limit_info': rate_limit_info,
                    'response_time': response_time
                })
                
                return False, None, response.status_code, response_time
            
            elif response.status_code >= 400:
                self.error_count += 1
                
                try:
                    error_data = response.json()
                except:
                    error_data = response.text
                
                self.error_responses.append({
                    'timestamp': datetime.now().isoformat(),
                    'description': description,
                    'status_code': response.status_code,
                    'error': error_data,
                    'response_time': response_time
                })
                
                return False, error_data, response.status_code, response_time
            
            else:
                self.success_count += 1
                
                try:
                    data = response.json()
                except:
                    data = response.text
                
                return True, data, response.status_code, response_time
        
        except Exception as e:
            response_time = time.time() - start_time
            self.error_count += 1
            
            self.error_responses.append({
                'timestamp': datetime.now().isoformat(),
                'description': description,
                'status_code': 0,
                'error': str(e),
                'response_time': response_time
            })
            
            return False, str(e), 0, response_time
    
    def test_basic_endpoints(self):
        """Test basic API endpoints"""
        print("🔍 Testing Basic API Endpoints")
        print("=" * 50)
        
        # Test 1: Page info
        print("1. Testing page info...")
        success, data, status, time_taken = self.make_request(
            'GET', 
            self.endpoints['page_info'],
            {'access_token': self.access_token},
            'page_info'
        )
        
        if success:
            print(f"   ✅ Page info: {data.get('name', 'Unknown')} ({time_taken:.3f}s)")
        else:
            print(f"   ❌ Page info failed: {status} ({time_taken:.3f}s)")
        
        # Test 2: Get posts
        print("2. Testing posts retrieval...")
        success, data, status, time_taken = self.make_request(
            'GET',
            self.endpoints['posts'],
            {
                'access_token': self.access_token,
                'limit': 10,
                'fields': 'id,message,created_time'
            },
            'get_posts'
        )
        
        if success:
            posts = data.get('data', [])
            print(f"   ✅ Posts retrieved: {len(posts)} posts ({time_taken:.3f}s)")
            return posts
        else:
            print(f"   ❌ Posts retrieval failed: {status} ({time_taken:.3f}s)")
            return []
    
    def test_comments_retrieval(self, posts, max_posts=5):
        """Test comments retrieval from posts"""
        print(f"\n🔍 Testing Comments Retrieval (max {max_posts} posts)")
        print("=" * 50)
        
        comments_data = []
        
        for i, post in enumerate(posts[:max_posts]):
            post_id = post['id']
            print(f"{i+1}. Testing comments for post {post_id}...")
            
            comments_url = self.endpoints['comments'].format(post_id=post_id)
            success, data, status, time_taken = self.make_request(
                'GET',
                comments_url,
                {
                    'access_token': self.access_token,
                    'limit': 50,
                    'fields': 'id,message,from,created_time'
                },
                f'get_comments_post_{i+1}'
            )
            
            if success:
                comments = data.get('data', [])
                print(f"   ✅ Comments retrieved: {len(comments)} comments ({time_taken:.3f}s)")
                comments_data.extend(comments)
            else:
                print(f"   ❌ Comments retrieval failed: {status} ({time_taken:.3f}s)")
        
        return comments_data
    
    def test_rapid_requests(self, endpoint_type='posts', duration_seconds=60, requests_per_second=10):
        """Test rapid requests untuk mencari rate limit"""
        print(f"\n🚀 Testing Rapid Requests - {endpoint_type.upper()}")
        print(f"Duration: {duration_seconds}s, Target: {requests_per_second} req/s")
        print("=" * 50)
        
        self.start_time = time.time()
        end_time = self.start_time + duration_seconds
        
        request_interval = 1.0 / requests_per_second
        last_request_time = 0
        
        while time.time() < end_time:
            current_time = time.time()
            
            # Rate limiting untuk maintain target RPS
            if current_time - last_request_time < request_interval:
                time.sleep(request_interval - (current_time - last_request_time))
            
            # Make request based on endpoint type
            if endpoint_type == 'posts':
                success, data, status, response_time = self.make_request(
                    'GET',
                    self.endpoints['posts'],
                    {
                        'access_token': self.access_token,
                        'limit': 5
                    },
                    f'rapid_posts_{self.request_count}'
                )
            
            elif endpoint_type == 'page_info':
                success, data, status, response_time = self.make_request(
                    'GET',
                    self.endpoints['page_info'],
                    {'access_token': self.access_token},
                    f'rapid_page_info_{self.request_count}'
                )
            
            last_request_time = time.time()
            
            # Progress update
            if self.request_count % 10 == 0:
                elapsed = time.time() - self.start_time
                current_rps = self.request_count / elapsed if elapsed > 0 else 0
                print(f"   Progress: {self.request_count} requests, {current_rps:.1f} RPS, "
                      f"{self.success_count} success, {self.error_count} errors, "
                      f"{self.rate_limit_hits} rate limits")
            
            # Stop if we hit rate limits consistently
            if self.rate_limit_hits > 5:
                print(f"   ⚠️ Stopping due to consistent rate limiting")
                break
        
        total_time = time.time() - self.start_time
        actual_rps = self.request_count / total_time if total_time > 0 else 0
        
        print(f"\n📊 Rapid Test Results:")
        print(f"   Total Requests: {self.request_count}")
        print(f"   Success: {self.success_count}")
        print(f"   Errors: {self.error_count}")
        print(f"   Rate Limits: {self.rate_limit_hits}")
        print(f"   Actual RPS: {actual_rps:.2f}")
        print(f"   Duration: {total_time:.2f}s")
    
    def test_comment_moderation_simulation(self, comments, max_operations=20):
        """Simulate comment moderation operations"""
        print(f"\n🛡️ Testing Comment Moderation Simulation")
        print(f"Max operations: {max_operations}")
        print("=" * 50)
        
        if not comments:
            print("❌ No comments available for testing")
            return
        
        moderation_results = {
            'read_operations': 0,
            'delete_attempts': 0,
            'delete_success': 0,
            'delete_failures': 0,
            'rate_limits': 0
        }
        
        for i, comment in enumerate(comments[:max_operations]):
            comment_id = comment['id']
            message = comment.get('message', 'No message')[:50]
            
            print(f"{i+1}. Processing comment {comment_id}...")
            print(f"   Message: {message}...")
            
            # Simulate spam detection (read operation)
            moderation_results['read_operations'] += 1
            
            # Simulate deletion attempt (only for demo - we won't actually delete)
            print(f"   🔍 Simulating deletion check...")
            
            # Test DELETE endpoint (but don't actually delete - just check permissions)
            delete_url = self.endpoints['delete_comment'].format(comment_id=comment_id)
            
            # Instead of actual deletion, just test the endpoint accessibility
            success, data, status, response_time = self.make_request(
                'GET',  # Use GET instead of DELETE for safety
                delete_url,
                {'access_token': self.access_token},
                f'check_delete_permission_{i+1}'
            )
            
            moderation_results['delete_attempts'] += 1
            
            if success:
                moderation_results['delete_success'] += 1
                print(f"   ✅ Delete permission check passed ({response_time:.3f}s)")
            else:
                moderation_results['delete_failures'] += 1
                if status == 429:
                    moderation_results['rate_limits'] += 1
                    print(f"   ⚠️ Rate limited ({response_time:.3f}s)")
                else:
                    print(f"   ❌ Delete check failed: {status} ({response_time:.3f}s)")
            
            # Add delay to avoid overwhelming the API
            time.sleep(0.5)
        
        print(f"\n📊 Moderation Simulation Results:")
        for key, value in moderation_results.items():
            print(f"   {key.replace('_', ' ').title()}: {value}")
    
    def generate_report(self):
        """Generate comprehensive test report"""
        print(f"\n📋 COMPREHENSIVE API TEST REPORT")
        print("=" * 60)
        
        total_time = time.time() - self.start_time if self.start_time else 0
        avg_response_time = sum(self.request_times) / len(self.request_times) if self.request_times else 0
        
        print(f"📊 Overall Statistics:")
        print(f"   Total Requests: {self.request_count}")
        print(f"   Successful: {self.success_count} ({self.success_count/self.request_count*100:.1f}%)")
        print(f"   Failed: {self.error_count} ({self.error_count/self.request_count*100:.1f}%)")
        print(f"   Rate Limited: {self.rate_limit_hits}")
        print(f"   Average Response Time: {avg_response_time:.3f}s")
        print(f"   Total Test Duration: {total_time:.2f}s")
        
        if total_time > 0:
            print(f"   Average RPS: {self.request_count/total_time:.2f}")
        
        # Response time analysis
        if self.request_times:
            self.request_times.sort()
            p50 = self.request_times[len(self.request_times)//2]
            p95 = self.request_times[int(len(self.request_times)*0.95)]
            p99 = self.request_times[int(len(self.request_times)*0.99)]
            
            print(f"\n⏱️ Response Time Percentiles:")
            print(f"   P50 (median): {p50:.3f}s")
            print(f"   P95: {p95:.3f}s")
            print(f"   P99: {p99:.3f}s")
            print(f"   Min: {min(self.request_times):.3f}s")
            print(f"   Max: {max(self.request_times):.3f}s")
        
        # Error analysis
        if self.error_responses:
            print(f"\n❌ Error Analysis:")
            error_types = defaultdict(int)
            for error in self.error_responses:
                if error['status_code'] == 429:
                    error_types['Rate Limited'] += 1
                elif error['status_code'] >= 500:
                    error_types['Server Error'] += 1
                elif error['status_code'] >= 400:
                    error_types['Client Error'] += 1
                else:
                    error_types['Network Error'] += 1
            
            for error_type, count in error_types.items():
                print(f"   {error_type}: {count}")
        
        # Rate limiting insights
        if self.rate_limit_hits > 0:
            print(f"\n⚠️ Rate Limiting Insights:")
            print(f"   Rate limit hits: {self.rate_limit_hits}")
            print(f"   Percentage of requests rate limited: {self.rate_limit_hits/self.request_count*100:.1f}%")
            
            # Show rate limit headers if available
            rate_limited_errors = [e for e in self.error_responses if e['status_code'] == 429]
            if rate_limited_errors:
                latest_rate_limit = rate_limited_errors[-1]
                rate_limit_info = latest_rate_limit.get('rate_limit_info', {})
                
                print(f"   Latest rate limit info:")
                for key, value in rate_limit_info.items():
                    if value:
                        print(f"     {key}: {value}")
        
        # Recommendations
        print(f"\n💡 Recommendations:")
        if self.rate_limit_hits == 0:
            print(f"   ✅ No rate limiting detected - current usage is within limits")
        elif self.rate_limit_hits < self.request_count * 0.1:
            print(f"   ⚠️ Occasional rate limiting - consider adding delays between requests")
        else:
            print(f"   🚨 Frequent rate limiting - significantly reduce request frequency")
        
        if avg_response_time > 2.0:
            print(f"   ⚠️ High average response time - consider optimizing requests")
        
        success_rate = self.success_count / self.request_count * 100 if self.request_count > 0 else 0
        if success_rate < 95:
            print(f"   ⚠️ Low success rate ({success_rate:.1f}%) - investigate error causes")

def main():
    """Main test function"""
    print("🧪 Facebook Graph API Rate Limit Tester")
    print("=" * 60)
    
    try:
        tester = GraphAPILimitTester()
        
        # Test 1: Basic endpoints
        posts = tester.test_basic_endpoints()
        
        # Test 2: Comments retrieval
        comments = tester.test_comments_retrieval(posts, max_posts=3)
        
        # Test 3: Comment moderation simulation
        if comments:
            tester.test_comment_moderation_simulation(comments, max_operations=10)
        
        # Test 4: Rapid requests test
        print(f"\n⚠️ Starting rapid requests test in 5 seconds...")
        print("This will test rate limits - press Ctrl+C to skip")
        
        try:
            time.sleep(5)
            tester.test_rapid_requests('posts', duration_seconds=30, requests_per_second=5)
        except KeyboardInterrupt:
            print("\n⏭️ Rapid test skipped by user")
        
        # Generate final report
        tester.generate_report()
        
    except KeyboardInterrupt:
        print("\n⏹️ Test interrupted by user")
    except Exception as e:
        print(f"\n❌ Test failed: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
