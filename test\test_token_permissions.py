#!/usr/bin/env python3
"""
Test Facebook Token Permissions
Verify if token has required permissions for comment deletion
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import requests
import json

def test_token_permissions():
    """Test Facebook token permissions"""
    print("🔍 Facebook Token Permissions Test")
    print("=" * 50)
    
    # Get credentials from environment
    page_id = os.getenv('PAGE_ID')
    page_access_token = os.getenv('PAGE_ACCESS_TOKEN')
    
    if not page_id or not page_access_token:
        print("❌ Missing Facebook credentials")
        print("Please set PAGE_ID and PAGE_ACCESS_TOKEN in .env file")
        return
    
    print(f"📋 Configuration:")
    print(f"  Page ID: {page_id}")
    print(f"  Token: {page_access_token[:20]}...")
    
    base_url = "https://graph.facebook.com/v18.0"
    
    # Test 1: Basic token info
    print(f"\n🔍 Test 1: Basic Token Info")
    print("-" * 30)
    
    try:
        response = requests.get(
            f"{base_url}/me",
            params={
                'access_token': page_access_token,
                'fields': 'id,name,category'
            }
        )
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Token Valid")
            print(f"  Name: {data.get('name', 'Unknown')}")
            print(f"  ID: {data.get('id', 'Unknown')}")
            print(f"  Category: {data.get('category', 'Unknown')}")
        else:
            print(f"❌ Token Invalid: {response.status_code}")
            print(f"  Response: {response.text}")
            return
            
    except Exception as e:
        print(f"❌ Error testing token: {str(e)}")
        return
    
    # Test 2: Page permissions
    print(f"\n🔍 Test 2: Page Access")
    print("-" * 30)
    
    try:
        response = requests.get(
            f"{base_url}/{page_id}",
            params={
                'access_token': page_access_token,
                'fields': 'id,name,category,access_token'
            }
        )
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Page Access OK")
            print(f"  Page Name: {data.get('name', 'Unknown')}")
            print(f"  Page ID: {data.get('id', 'Unknown')}")
            print(f"  Has Access Token: {'access_token' in data}")
        else:
            print(f"❌ Page Access Failed: {response.status_code}")
            print(f"  Response: {response.text}")
            
    except Exception as e:
        print(f"❌ Error accessing page: {str(e)}")
    
    # Test 3: Read permissions (get posts)
    print(f"\n🔍 Test 3: Read Permissions (Posts)")
    print("-" * 30)
    
    try:
        response = requests.get(
            f"{base_url}/{page_id}/posts",
            params={
                'access_token': page_access_token,
                'limit': 1,
                'fields': 'id,message,created_time'
            }
        )
        
        if response.status_code == 200:
            data = response.json()
            posts = data.get('data', [])
            print(f"✅ Read Posts: OK ({len(posts)} posts)")
            if posts:
                print(f"  Latest Post ID: {posts[0]['id']}")
        else:
            print(f"❌ Read Posts Failed: {response.status_code}")
            print(f"  Response: {response.text}")
            
    except Exception as e:
        print(f"❌ Error reading posts: {str(e)}")
    
    # Test 4: Read permissions (get comments)
    print(f"\n🔍 Test 4: Read Permissions (Comments)")
    print("-" * 30)
    
    try:
        # Get a post first
        posts_response = requests.get(
            f"{base_url}/{page_id}/posts",
            params={
                'access_token': page_access_token,
                'limit': 1
            }
        )
        
        if posts_response.status_code == 200:
            posts = posts_response.json().get('data', [])
            if posts:
                post_id = posts[0]['id']
                
                comments_response = requests.get(
                    f"{base_url}/{post_id}/comments",
                    params={
                        'access_token': page_access_token,
                        'limit': 1,
                        'fields': 'id,message,from'
                    }
                )
                
                if comments_response.status_code == 200:
                    comments_data = comments_response.json()
                    comments = comments_data.get('data', [])
                    print(f"✅ Read Comments: OK ({len(comments)} comments)")
                    if comments:
                        print(f"  Sample Comment ID: {comments[0]['id']}")
                else:
                    print(f"❌ Read Comments Failed: {comments_response.status_code}")
            else:
                print("⚠️ No posts found to test comments")
        else:
            print(f"❌ Cannot get posts for comment test")
            
    except Exception as e:
        print(f"❌ Error reading comments: {str(e)}")
    
    # Test 5: DELETE permissions (the critical test)
    print(f"\n🔍 Test 5: DELETE Permissions (Critical Test)")
    print("-" * 30)
    
    try:
        # Get a comment to test delete
        posts_response = requests.get(
            f"{base_url}/{page_id}/posts",
            params={
                'access_token': page_access_token,
                'limit': 3
            }
        )
        
        test_comment_id = None
        if posts_response.status_code == 200:
            posts = posts_response.json().get('data', [])
            for post in posts:
                post_id = post['id']
                
                comments_response = requests.get(
                    f"{base_url}/{post_id}/comments",
                    params={
                        'access_token': page_access_token,
                        'limit': 1
                    }
                )
                
                if comments_response.status_code == 200:
                    comments = comments_response.json().get('data', [])
                    if comments:
                        test_comment_id = comments[0]['id']
                        break
        
        if test_comment_id:
            print(f"Testing DELETE on comment: {test_comment_id}")
            
            # Try DELETE request
            delete_response = requests.delete(
                f"{base_url}/{test_comment_id}",
                params={'access_token': page_access_token}
            )
            
            print(f"DELETE Response:")
            print(f"  Status Code: {delete_response.status_code}")
            
            if delete_response.status_code == 200:
                print("✅ DELETE Permission: OK")
                print("  ⚠️ Note: Comment may have been actually deleted!")
            elif delete_response.status_code == 403:
                print("❌ DELETE Permission: DENIED")
                print("  🚨 TOKEN LACKS DELETE PERMISSIONS")
            elif delete_response.status_code == 400:
                try:
                    error_data = delete_response.json()
                    error_code = error_data.get('error', {}).get('code', 'unknown')
                    error_message = error_data.get('error', {}).get('message', 'unknown')
                    print(f"❌ DELETE Error ({error_code}): {error_message}")
                except:
                    print(f"❌ DELETE Error: {delete_response.text}")
            else:
                print(f"❌ DELETE Unexpected Response: {delete_response.status_code}")
                print(f"  Response: {delete_response.text}")
                
        else:
            print("⚠️ No comments found to test DELETE permissions")
            
    except Exception as e:
        print(f"❌ Error testing DELETE permissions: {str(e)}")
    
    # Summary and recommendations
    print(f"\n📋 SUMMARY & RECOMMENDATIONS")
    print("=" * 50)
    
    print(f"🔍 Current Status:")
    print(f"  ✅ Token is valid and working")
    print(f"  ✅ Can read posts and comments")
    print(f"  ❌ CANNOT delete comments (Permission Denied)")
    
    print(f"\n💡 Required Actions:")
    print(f"  1. 🔑 Update token permissions:")
    print(f"     - pages_manage_posts")
    print(f"     - pages_manage_engagement")
    
    print(f"\n  2. 🛠️ How to fix:")
    print(f"     a) Go to Facebook Graph API Explorer")
    print(f"     b) Generate new token with required permissions")
    print(f"     c) Update .env file with new token")
    
    print(f"\n  3. ✅ Verify page role:")
    print(f"     - Ensure you are Admin or Editor of the page")
    print(f"     - Moderator role CANNOT delete comments")
    
    print(f"\n🔗 Useful Links:")
    print(f"  Graph API Explorer: https://developers.facebook.com/tools/explorer/")
    print(f"  App Dashboard: https://developers.facebook.com/apps/")
    print(f"  Permissions Guide: https://developers.facebook.com/docs/permissions/reference")

if __name__ == "__main__":
    test_token_permissions()
