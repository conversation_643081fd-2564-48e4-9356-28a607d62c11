# Facebook Graph API Testing Guide

Panduan lengkap untuk testing rate limits dan performance Facebook Graph API untuk moderasi komentar.

## 📋 Overview

Terdapat 3 script testing yang berbeda untuk kebutuhan yang berbeda:

1. **Quick API Test** - Test cepat untuk validasi dasar
2. **Graph API Limits Test** - Test komprehensif untuk rate limits
3. **Moderation Limits Test** - Test khusus untuk workflow moderasi

## 🚀 Quick Start

### Prerequisites

1. Pastikan `.env` file sudah dikonfigurasi:
```env
PAGE_ID=your_facebook_page_id
PAGE_ACCESS_TOKEN=your_page_access_token
```

2. Install dependencies:
```bash
pip install requests python-dotenv
```

## 📊 Test Scripts

### 1. Quick API Test

**File:** `scripts/quick_api_test.py`

**Tujuan:** Test cepat untuk validasi dasar API access dan performance

**Fitur:**
- ✅ Basic page access test
- ✅ Posts retrieval test
- ✅ Comments retrieval test
- ✅ Rapid requests test (10 requests)
- ✅ Simple stress test (20 requests in 10s)

**Cara <PERSON>an:**
```bash
python scripts/quick_api_test.py
```

**Output:**
- Success rate
- Response times
- Rate limiting detection
- Quick recommendations

**Waktu:** ~30 detik

---

### 2. Graph API Limits Test

**File:** `scripts/test_graph_api_limits.py`

**Tujuan:** Test komprehensif untuk menemukan rate limits dan performance boundaries

**Fitur:**
- 🔍 Basic endpoints testing
- 📄 Comments retrieval testing
- 🚀 Rapid requests testing (configurable RPS)
- 🛡️ Comment moderation simulation
- 📊 Comprehensive reporting dengan percentiles
- ⚠️ Rate limit headers analysis

**Cara Menjalankan:**
```bash
python scripts/test_graph_api_limits.py
```

**Konfigurasi:**
- Duration: 30-60 detik
- RPS: 5-10 requests per second
- Max posts: 5 posts
- Max operations: 20 operations

**Output:**
- Detailed statistics
- Response time percentiles (P50, P95, P99)
- Error analysis by type
- Rate limiting insights
- Performance recommendations

**Waktu:** ~2-3 menit

---

### 3. Moderation Limits Test

**File:** `scripts/test_moderation_limits.py`

**Tujuan:** Test khusus untuk workflow moderasi komentar dengan concurrent processing

**Fitur:**
- 🔄 Concurrent comment reads
- 🛡️ Realistic moderation workflow simulation
- 🧵 Multi-threading support
- 📊 Thread-safe statistics
- 🎯 Moderation-specific metrics

**Cara Menjalankan:**
```bash
python scripts/test_moderation_limits.py
```

**Konfigurasi:**
- Max workers: 3-5 threads
- Duration: 30-60 detik
- Max operations: 30 operations
- Comments collected: 30 comments

**Output:**
- Concurrent performance metrics
- Moderation workflow success rates
- Thread-safe error tracking
- Moderation-specific recommendations

**Waktu:** ~2-4 menit

## 📈 Understanding Results

### Success Rates

- **95-100%**: Excellent - API is stable
- **80-94%**: Good - Minor issues
- **60-79%**: Fair - Some problems
- **<60%**: Poor - Significant issues

### Response Times

- **<0.5s**: Excellent
- **0.5-1.0s**: Good
- **1.0-2.0s**: Fair
- **>2.0s**: Poor

### Rate Limiting

- **0%**: Safe to increase request rate
- **1-5%**: Near optimal rate
- **5-20%**: Consider reducing rate
- **>20%**: Significantly reduce rate

## 🎯 Recommended Testing Sequence

### For Initial Setup:
```bash
# 1. Quick validation
python scripts/quick_api_test.py

# 2. If quick test passes, run comprehensive test
python scripts/test_graph_api_limits.py
```

### For Production Optimization:
```bash
# Run moderation-specific test
python scripts/test_moderation_limits.py
```

### For Troubleshooting:
```bash
# Run all tests in sequence
python scripts/quick_api_test.py
python scripts/test_graph_api_limits.py
python scripts/test_moderation_limits.py
```

## 🔧 Customization

### Adjusting Test Parameters

**Quick Test:**
- Modify `range(10)` untuk mengubah jumlah rapid requests
- Modify `time.sleep(0.2)` untuk mengubah delay

**Graph API Limits Test:**
- `duration_seconds=60` - durasi test
- `requests_per_second=10` - target RPS
- `max_posts=5` - maksimal posts untuk test

**Moderation Limits Test:**
- `max_workers=5` - jumlah concurrent threads
- `max_comments=50` - maksimal comments untuk collect
- `max_operations=30` - maksimal operasi moderasi

### Adding Custom Tests

Tambahkan test custom di dalam class yang sesuai:

```python
def test_custom_scenario(self):
    """Custom test scenario"""
    # Your custom test logic here
    pass
```

## ⚠️ Safety Notes

1. **Rate Limiting**: Semua test dirancang untuk menghormati rate limits
2. **No Actual Deletion**: Test tidak akan menghapus komentar sungguhan
3. **Timeout Handling**: Semua requests memiliki timeout
4. **Graceful Interruption**: Gunakan Ctrl+C untuk menghentikan test

## 📊 Expected Rate Limits

Berdasarkan Facebook documentation:

- **Page-level calls**: ~200 calls per hour per user
- **App-level calls**: Varies by app usage
- **Concurrent requests**: 10-25 concurrent requests
- **Burst requests**: Short bursts allowed, then throttled

## 🎯 Optimization Recommendations

### For High-Volume Moderation:

1. **Batch Processing**: Process comments in batches
2. **Rate Limiting**: Add delays between operations
3. **Concurrent Limits**: Use 3-5 concurrent workers max
4. **Error Handling**: Implement exponential backoff
5. **Monitoring**: Track rate limit headers

### Sample Production Settings:

```python
# Recommended settings for production
CONCURRENT_WORKERS = 3
REQUESTS_PER_SECOND = 2
BATCH_SIZE = 10
RETRY_DELAY = 2  # seconds
MAX_RETRIES = 3
```

## 🐛 Troubleshooting

### Common Issues:

1. **403 Permission Denied**
   - Check token permissions
   - Verify page role (Admin/Editor required)

2. **429 Rate Limited**
   - Reduce request frequency
   - Add delays between requests

3. **Network Timeouts**
   - Check internet connection
   - Increase timeout values

4. **Token Expired**
   - Regenerate access token
   - Check token expiration

### Debug Mode:

Add debug logging untuk troubleshooting:

```python
import logging
logging.basicConfig(level=logging.DEBUG)
```

## 📞 Support

Jika mengalami issues:

1. Check error messages dalam output
2. Verify credentials di `.env` file
3. Test dengan `quick_api_test.py` terlebih dahulu
4. Check Facebook API status page
5. Review token permissions di Facebook Developer Console
