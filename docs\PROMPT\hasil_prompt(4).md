# Kebutuhan Fungsional: <PERSON><PERSON><PERSON> Komentar Postingan Secara On-Demand

**Deskripsi:** Sistem harus memungkinkan pengguna untuk memilih postingan spesifik dari platform eksternal dan memicu analisis spam untuk semua komentar pada postingan tersebut secara on-demand.

---

## 1. Use Case Description

Dokumen ini merinci interaksi, alur, dan kondisi untuk kasus penggunaan analisis komentar on-demand.

| | |
|---|---|
| **Use Case Name** | **UC-04: Menganalisis Komentar Postingan Secara On-Demand** |
| **Actor** | Pengguna (User) |
| **Description** | Use case ini menjelaskan bagaimana **Pengguna** memilih sebuah postingan spesifik dari daftar, kemudian memicu sistem untuk mengambil dan menganalisis semua komentar pada postingan tersebut. |
| **Preconditions** | Pengguna berada di halaman "Manual Check". Kredensial API Facebook telah dikonfigurasi dengan benar. |
| **Postconditions** | **Success:** Hasil analisis untuk semua komentar pada postingan yang dipilih ditampilkan kepada **Pengguna**. <br> **Failure:** Pesan kesalahan ditampilkan jika gagal mengambil data dari API atau terjadi kesalahan internal. |

### Main Flow (Basic Path)
| Langkah | Aksi Aktor (Pengguna) | Respon Sistem |
|---|---|---|
| 1 | Membuka halaman "Manual Check". | |
| 2 | | Mengambil daftar postingan terbaru dari API Facebook dan menampilkannya dalam bentuk pilihan (dropdown/list). |
| 3 | Memilih salah satu postingan dari daftar. | |
| 4 | Menekan tombol "Check Post". | |
| 5 | | 1. Menerima permintaan untuk postingan yang dipilih. <br> 2. Memanggil API Facebook untuk mengambil semua komentar dari postingan tersebut. |
| 6 | | Untuk setiap komentar yang diterima, sistem memanggil model klasifikasi internal untuk menentukan apakah itu spam. |
| 7 | | Mengumpulkan hasil analisis (jumlah spam, detail per komentar). |
| 8 | | Menampilkan ringkasan dan detail hasil analisis di antarmuka. |
| 9 | Melihat hasil analisis yang ditampilkan. | |

### Alternative Flow (Tidak Ada Komentar)
| Langkah | Aksi Aktor (Pengguna) | Respon Sistem |
|---|---|---|
| 5a.1 | | Mengambil komentar dari API dan menemukan bahwa tidak ada komentar pada postingan tersebut. |
| 5a.2 | | Menampilkan pesan di antarmuka, misalnya: "Tidak ada komentar yang ditemukan pada postingan ini." |
| - | | Use case berakhir. |

---

## 2. Activity Diagram

Diagram ini menggambarkan alur kerja pengguna saat meminta analisis untuk sebuah postingan.

```plantuml
@startuml
title Activity Diagram: Analisis Komentar Postingan

|Pengguna|
start
:Membuka halaman "Manual Check";

|Sistem|
:Mengambil & menampilkan daftar postingan dari API;

|Pengguna|
:Memilih sebuah postingan;
:Menekan tombol "Check Post";

|Sistem|
partition "Proses Analisis Komentar" {
    :Mengambil daftar komentar dari API Facebook;
    if (Ada komentar?) then (Ya)
        :Ulangi untuk setiap komentar;
            :Panggil model klasifikasi internal;
            :Simpan hasil analisis;
        :end repeat;
        :Tampilkan ringkasan & detail hasil;
    else (Tidak)
        :Tampilkan pesan "Tidak ada komentar";
    endif
}

|Pengguna|
:Melihat hasil analisis;
stop

@enduml
```

---

## 3. Sequence Diagram

Diagram ini menunjukkan interaksi antar komponen saat pengguna meminta analisis komentar dari sebuah postingan.

```plantuml
@startuml
title Sequence Diagram: Analisis Komentar Postingan

actor Pengguna
participant "Antarmuka Web" as Frontend
participant "Sistem (Backend)" as Backend
participant "API Facebook" as FacebookAPI
participant "Model Klasifikasi" as Model

Pengguna -> Frontend: 1. Buka halaman "Manual Check"
activate Frontend

Frontend -> Backend: 2. getPosts()
activate Backend
Backend -> FacebookAPI: 3. fetchRecentPosts()
activate FacebookAPI
FacebookAPI --> Backend: 4. postList
deactivate FacebookAPI
Backend --> Frontend: 5. postList
deactivate Backend

Frontend -> Pengguna: 6. Tampilkan daftar postingan

Pengguna -> Frontend: 7. Pilih postingan & klik "Check"
Frontend -> Backend: 8. analyzePostComments(postId)
activate Backend

Backend -> FacebookAPI: 9. fetchComments(postId)
activate FacebookAPI
FacebookAPI --> Backend: 10. commentList
deactivate FacebookAPI

loop untuk setiap komentar
    Backend -> Model: 11. classify(comment.text)
    activate Model
    Model --> Backend: 12. classificationResult
    deactivate Model
end

Backend --> Frontend: 13. aggregatedResults
deactivate Backend

Frontend -> Pengguna: 14. Tampilkan hasil analisis
deactivate Frontend

@enduml
```
