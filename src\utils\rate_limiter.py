#!/usr/bin/env python3
"""
Facebook Graph API Rate Limiter
Implement rate limiting untuk DELETE operations
"""

import time
import json
from datetime import datetime, timedelta
from collections import deque
import threading

class FacebookRateLimiter:
    """Rate limiter khusus untuk Facebook Graph API"""
    
    def __init__(self):
        """Initialize rate limiter"""
        self.lock = threading.Lock()
        
        # Rate limiting windows
        self.delete_requests = deque()  # Track DELETE requests
        self.read_requests = deque()    # Track READ requests
        
        # Rate limits (conservative)
        self.DELETE_LIMIT_PER_MINUTE = 30    # 0.5 RPS
        self.DELETE_LIMIT_PER_HOUR = 1000    # ~0.28 RPS average
        self.READ_LIMIT_PER_MINUTE = 300     # 5 RPS
        
        # Minimum delays
        self.MIN_DELETE_DELAY = 2.0  # 2 seconds between deletes
        self.MIN_READ_DELAY = 0.2    # 0.2 seconds between reads
        
        # Last request times
        self.last_delete_time = 0
        self.last_read_time = 0
        
        # Usage tracking
        self.usage_stats = {
            'total_deletes': 0,
            'total_reads': 0,
            'rate_limited_deletes': 0,
            'rate_limited_reads': 0
        }
    
    def _cleanup_old_requests(self, request_queue, window_seconds):
        """Remove old requests outside the time window"""
        current_time = time.time()
        cutoff_time = current_time - window_seconds
        
        while request_queue and request_queue[0] < cutoff_time:
            request_queue.popleft()
    
    def can_make_delete_request(self):
        """
        Check if DELETE request can be made
        
        Returns:
            tuple: (can_proceed, wait_time)
        """
        with self.lock:
            current_time = time.time()
            
            # Clean up old requests
            self._cleanup_old_requests(self.delete_requests, 60)    # 1 minute
            self._cleanup_old_requests(self.delete_requests, 3600)  # 1 hour (approximate)
            
            # Check minimum delay since last delete
            time_since_last = current_time - self.last_delete_time
            if time_since_last < self.MIN_DELETE_DELAY:
                wait_time = self.MIN_DELETE_DELAY - time_since_last
                return False, wait_time
            
            # Check per-minute limit
            if len(self.delete_requests) >= self.DELETE_LIMIT_PER_MINUTE:
                # Calculate wait time until oldest request expires
                oldest_request = self.delete_requests[0]
                wait_time = 60 - (current_time - oldest_request)
                return False, max(wait_time, 0)
            
            return True, 0
    
    def can_make_read_request(self):
        """
        Check if READ request can be made
        
        Returns:
            tuple: (can_proceed, wait_time)
        """
        with self.lock:
            current_time = time.time()
            
            # Clean up old requests
            self._cleanup_old_requests(self.read_requests, 60)  # 1 minute
            
            # Check minimum delay since last read
            time_since_last = current_time - self.last_read_time
            if time_since_last < self.MIN_READ_DELAY:
                wait_time = self.MIN_READ_DELAY - time_since_last
                return False, wait_time
            
            # Check per-minute limit
            if len(self.read_requests) >= self.READ_LIMIT_PER_MINUTE:
                oldest_request = self.read_requests[0]
                wait_time = 60 - (current_time - oldest_request)
                return False, max(wait_time, 0)
            
            return True, 0
    
    def record_delete_request(self):
        """Record a DELETE request"""
        with self.lock:
            current_time = time.time()
            self.delete_requests.append(current_time)
            self.last_delete_time = current_time
            self.usage_stats['total_deletes'] += 1
    
    def record_read_request(self):
        """Record a READ request"""
        with self.lock:
            current_time = time.time()
            self.read_requests.append(current_time)
            self.last_read_time = current_time
            self.usage_stats['total_reads'] += 1
    
    def wait_for_delete_permission(self, max_wait=30):
        """
        Wait until DELETE request can be made
        
        Args:
            max_wait (int): Maximum wait time in seconds
            
        Returns:
            bool: True if permission granted, False if timeout
        """
        start_time = time.time()
        
        while time.time() - start_time < max_wait:
            can_proceed, wait_time = self.can_make_delete_request()
            
            if can_proceed:
                return True
            
            if wait_time > 0:
                print(f"⏰ Rate limited: waiting {wait_time:.1f}s for DELETE permission...")
                time.sleep(min(wait_time, 1.0))  # Sleep in 1s chunks
            else:
                time.sleep(0.1)  # Small delay before retry
        
        self.usage_stats['rate_limited_deletes'] += 1
        return False
    
    def wait_for_read_permission(self, max_wait=10):
        """
        Wait until READ request can be made
        
        Args:
            max_wait (int): Maximum wait time in seconds
            
        Returns:
            bool: True if permission granted, False if timeout
        """
        start_time = time.time()
        
        while time.time() - start_time < max_wait:
            can_proceed, wait_time = self.can_make_read_request()
            
            if can_proceed:
                return True
            
            if wait_time > 0:
                time.sleep(min(wait_time, 0.5))  # Sleep in 0.5s chunks
            else:
                time.sleep(0.05)  # Small delay before retry
        
        self.usage_stats['rate_limited_reads'] += 1
        return False
    
    def get_usage_stats(self):
        """Get current usage statistics"""
        with self.lock:
            current_time = time.time()
            
            # Clean up old requests for accurate counts
            self._cleanup_old_requests(self.delete_requests, 60)
            self._cleanup_old_requests(self.read_requests, 60)
            
            return {
                'delete_requests_last_minute': len(self.delete_requests),
                'read_requests_last_minute': len(self.read_requests),
                'delete_limit_per_minute': self.DELETE_LIMIT_PER_MINUTE,
                'read_limit_per_minute': self.READ_LIMIT_PER_MINUTE,
                'delete_usage_percentage': len(self.delete_requests) / self.DELETE_LIMIT_PER_MINUTE * 100,
                'read_usage_percentage': len(self.read_requests) / self.READ_LIMIT_PER_MINUTE * 100,
                'total_stats': self.usage_stats.copy()
            }
    
    def adjust_limits_based_on_headers(self, rate_limit_headers):
        """
        Adjust rate limits based on Facebook response headers
        
        Args:
            rate_limit_headers (dict): Rate limit headers from Facebook response
        """
        try:
            # Parse business usage if available
            business_usage = rate_limit_headers.get('x_business_use_case_usage')
            if business_usage:
                usage_data = json.loads(business_usage)
                
                # Extract usage percentages
                for app_id, usage_list in usage_data.items():
                    for usage in usage_list:
                        call_count = usage.get('call_count', 0)
                        total_time = usage.get('total_time', 0)
                        
                        # Adjust limits based on usage
                        if call_count > 80:  # High usage
                            self.DELETE_LIMIT_PER_MINUTE = max(10, self.DELETE_LIMIT_PER_MINUTE // 2)
                            self.MIN_DELETE_DELAY = min(5.0, self.MIN_DELETE_DELAY * 1.5)
                            print(f"⚠️ High API usage detected ({call_count}%), reducing DELETE rate")
                        
                        elif call_count > 60:  # Moderate usage
                            self.DELETE_LIMIT_PER_MINUTE = max(20, int(self.DELETE_LIMIT_PER_MINUTE * 0.8))
                            self.MIN_DELETE_DELAY = min(3.0, self.MIN_DELETE_DELAY * 1.2)
                            print(f"⚠️ Moderate API usage detected ({call_count}%), reducing DELETE rate")
                        
                        elif call_count < 20:  # Low usage
                            self.DELETE_LIMIT_PER_MINUTE = min(60, int(self.DELETE_LIMIT_PER_MINUTE * 1.1))
                            self.MIN_DELETE_DELAY = max(1.0, self.MIN_DELETE_DELAY * 0.9)
        
        except Exception as e:
            print(f"⚠️ Error parsing rate limit headers: {str(e)}")

# Global rate limiter instance
_rate_limiter = None

def get_rate_limiter():
    """Get global rate limiter instance"""
    global _rate_limiter
    if _rate_limiter is None:
        _rate_limiter = FacebookRateLimiter()
    return _rate_limiter

def rate_limited_delete(func):
    """
    Decorator untuk DELETE operations dengan rate limiting
    
    Usage:
        @rate_limited_delete
        def delete_comment(comment_id):
            # Your delete logic here
            pass
    """
    def wrapper(*args, **kwargs):
        rate_limiter = get_rate_limiter()
        
        # Wait for permission
        if not rate_limiter.wait_for_delete_permission():
            raise Exception("DELETE rate limit exceeded, request timed out")
        
        # Record the request
        rate_limiter.record_delete_request()
        
        try:
            # Execute the function
            result = func(*args, **kwargs)
            return result
        except Exception as e:
            # If it's a rate limit error, adjust our limits
            if "rate limit" in str(e).lower() or "429" in str(e):
                print("🚨 Facebook rate limit hit, adjusting internal limits")
                rate_limiter.MIN_DELETE_DELAY *= 1.5
                rate_limiter.DELETE_LIMIT_PER_MINUTE = max(5, rate_limiter.DELETE_LIMIT_PER_MINUTE // 2)
            raise e
    
    return wrapper

def rate_limited_read(func):
    """
    Decorator untuk READ operations dengan rate limiting
    
    Usage:
        @rate_limited_read
        def get_comments(post_id):
            # Your read logic here
            pass
    """
    def wrapper(*args, **kwargs):
        rate_limiter = get_rate_limiter()
        
        # Wait for permission
        if not rate_limiter.wait_for_read_permission():
            raise Exception("READ rate limit exceeded, request timed out")
        
        # Record the request
        rate_limiter.record_read_request()
        
        # Execute the function
        return func(*args, **kwargs)
    
    return wrapper
