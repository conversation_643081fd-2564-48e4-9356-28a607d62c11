#!/usr/bin/env python3
"""
Quick Facebook Graph API Test
Test cepat untuk melihat rate limits dan performance
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import time
import requests
from datetime import datetime
from dotenv import load_dotenv

def quick_api_test():
    """Quick test untuk Facebook Graph API"""
    print("⚡ Quick Facebook Graph API Test")
    print("=" * 40)
    
    # Load credentials
    load_dotenv()
    page_id = os.getenv('PAGE_ID')
    access_token = os.getenv('PAGE_ACCESS_TOKEN')
    
    if not page_id or not access_token:
        print("❌ PAGE_ID dan PAGE_ACCESS_TOKEN harus diset di .env")
        return
    
    base_url = "https://graph.facebook.com/v18.0"
    
    # Test results
    results = {
        'total_requests': 0,
        'successful': 0,
        'failed': 0,
        'rate_limited': 0,
        'response_times': []
    }
    
    def make_test_request(url, params, description):
        """Make a test request and track results"""
        start_time = time.time()
        
        try:
            response = requests.get(url, params=params, timeout=10)
            response_time = time.time() - start_time
            
            results['total_requests'] += 1
            results['response_times'].append(response_time)
            
            if response.status_code == 200:
                results['successful'] += 1
                print(f"✅ {description}: OK ({response_time:.3f}s)")
                return True, response.json()
            elif response.status_code == 429:
                results['rate_limited'] += 1
                results['failed'] += 1
                print(f"⚠️ {description}: Rate Limited ({response_time:.3f}s)")
                return False, None
            else:
                results['failed'] += 1
                print(f"❌ {description}: Error {response.status_code} ({response_time:.3f}s)")
                return False, None
                
        except Exception as e:
            response_time = time.time() - start_time
            results['total_requests'] += 1
            results['failed'] += 1
            results['response_times'].append(response_time)
            print(f"❌ {description}: {str(e)} ({response_time:.3f}s)")
            return False, None
    
    # Test 1: Basic page info
    print("\n1. Testing basic page access...")
    success, data = make_test_request(
        f"{base_url}/{page_id}",
        {'access_token': access_token, 'fields': 'id,name'},
        "Page Info"
    )
    
    if success:
        print(f"   Page: {data.get('name', 'Unknown')}")
    
    # Test 2: Get posts
    print("\n2. Testing posts retrieval...")
    success, posts_data = make_test_request(
        f"{base_url}/{page_id}/posts",
        {'access_token': access_token, 'limit': 5, 'fields': 'id,message'},
        "Posts"
    )
    
    posts = []
    if success:
        posts = posts_data.get('data', [])
        print(f"   Found {len(posts)} posts")
    
    # Test 3: Get comments
    if posts:
        print("\n3. Testing comments retrieval...")
        for i, post in enumerate(posts[:2]):  # Test first 2 posts
            success, comments_data = make_test_request(
                f"{base_url}/{post['id']}/comments",
                {'access_token': access_token, 'limit': 10, 'fields': 'id,message'},
                f"Comments Post {i+1}"
            )
            
            if success:
                comments = comments_data.get('data', [])
                print(f"   Post {i+1}: {len(comments)} comments")
    
    # Test 4: Rapid requests test
    print("\n4. Testing rapid requests (10 requests)...")
    rapid_start = time.time()
    
    for i in range(10):
        success, data = make_test_request(
            f"{base_url}/{page_id}",
            {'access_token': access_token, 'fields': 'id'},
            f"Rapid {i+1}"
        )
        
        # Small delay
        time.sleep(0.2)
    
    rapid_duration = time.time() - rapid_start
    rapid_rps = 10 / rapid_duration
    
    print(f"   Rapid test completed in {rapid_duration:.2f}s ({rapid_rps:.1f} RPS)")
    
    # Results summary
    print(f"\n📊 Test Summary:")
    print("=" * 30)
    print(f"Total Requests: {results['total_requests']}")
    print(f"Successful: {results['successful']}")
    print(f"Failed: {results['failed']}")
    print(f"Rate Limited: {results['rate_limited']}")
    
    if results['response_times']:
        avg_time = sum(results['response_times']) / len(results['response_times'])
        print(f"Average Response Time: {avg_time:.3f}s")
        print(f"Min Response Time: {min(results['response_times']):.3f}s")
        print(f"Max Response Time: {max(results['response_times']):.3f}s")
    
    success_rate = (results['successful'] / results['total_requests'] * 100) if results['total_requests'] > 0 else 0
    print(f"Success Rate: {success_rate:.1f}%")
    
    # Recommendations
    print(f"\n💡 Quick Assessment:")
    if results['rate_limited'] == 0:
        print("✅ No rate limiting detected")
        print("✅ API access is working normally")
    else:
        rate_limit_pct = results['rate_limited'] / results['total_requests'] * 100
        print(f"⚠️ Rate limiting detected: {rate_limit_pct:.1f}% of requests")
        print("⚠️ Consider reducing request frequency")
    
    if success_rate >= 95:
        print("✅ High success rate - API is stable")
    elif success_rate >= 80:
        print("⚠️ Moderate success rate - some issues detected")
    else:
        print("🚨 Low success rate - significant issues")
    
    if avg_time > 2.0:
        print("⚠️ High response times - network or API issues")
    else:
        print("✅ Good response times")

def stress_test():
    """Simple stress test"""
    print("\n⚡ Quick Stress Test")
    print("=" * 30)
    
    load_dotenv()
    page_id = os.getenv('PAGE_ID')
    access_token = os.getenv('PAGE_ACCESS_TOKEN')
    
    if not page_id or not access_token:
        print("❌ Credentials not found")
        return
    
    base_url = f"https://graph.facebook.com/v18.0/{page_id}"
    
    print("Testing 20 requests in 10 seconds...")
    
    start_time = time.time()
    success_count = 0
    rate_limit_count = 0
    
    for i in range(20):
        try:
            response = requests.get(
                base_url,
                params={'access_token': access_token, 'fields': 'id'},
                timeout=5
            )
            
            if response.status_code == 200:
                success_count += 1
                print(f"✅ {i+1}/20", end=" ")
            elif response.status_code == 429:
                rate_limit_count += 1
                print(f"⚠️ {i+1}/20", end=" ")
            else:
                print(f"❌ {i+1}/20", end=" ")
            
            if (i + 1) % 5 == 0:
                print()  # New line every 5 requests
            
        except Exception as e:
            print(f"❌ {i+1}/20", end=" ")
        
        time.sleep(0.5)  # 2 RPS
    
    duration = time.time() - start_time
    actual_rps = 20 / duration
    
    print(f"\n\nStress Test Results:")
    print(f"Duration: {duration:.2f}s")
    print(f"Actual RPS: {actual_rps:.1f}")
    print(f"Success: {success_count}/20 ({success_count/20*100:.1f}%)")
    print(f"Rate Limited: {rate_limit_count}/20 ({rate_limit_count/20*100:.1f}%)")
    
    if rate_limit_count == 0:
        print("✅ No rate limiting at 2 RPS")
    else:
        print(f"⚠️ Rate limiting detected at 2 RPS")

def main():
    """Main function"""
    try:
        quick_api_test()
        
        print("\n" + "="*50)
        stress_choice = input("\nRun stress test? (y/n): ").strip().lower()
        
        if stress_choice == 'y':
            stress_test()
        
    except KeyboardInterrupt:
        print("\n⏹️ Test interrupted")
    except Exception as e:
        print(f"\n❌ Error: {str(e)}")

if __name__ == "__main__":
    main()
