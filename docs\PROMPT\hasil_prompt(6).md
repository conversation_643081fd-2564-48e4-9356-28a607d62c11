# Kebutuhan Fungsional: Monitoring Otomatis dari Facebook

Dokumen ini berisi Use Case Description, Activity Diagram, dan Sequence Diagram untuk kebutuhan fungsional: *"Sistem harus dapat terhubung ke platform eksternal (Facebook) untuk memonitor dan mengambil data (misalnya, komentar pada postingan) secara otomatis untuk dianalisis."*

---

## 1. Use Case Description

### **UC-06: Memonitor Komentar Facebook Secara Otomatis**

*   **Actor:** Pengguna (User/Admin)
*   **Description:** Use case ini menjelaskan bagaimana **Pengguna** mengaktifkan sebuah proses monitoring di latar belakang. Setelah diaktifkan, **Sistem** akan secara periodik mengambil komentar baru dari target Facebook yang telah ditentukan, menganalisisnya, dan mengambil tindakan (menghapus atau menandai untuk ditinjau) jika terdeteksi sebagai spam.
*   **Preconditions:**
    *   Sistem telah dikonfigurasi dengan kredensial API Facebook yang valid.
    *   Target monitor (misalnya, ID halaman atau postingan) telah ditentukan di pengaturan.
*   **Postconditions:**
    *   **Success:** Proses monitoring berjalan di latar belakang, secara aktif menganalisis dan menindaklanjuti komentar baru sesuai pengaturan.
    *   **Failure:** Proses monitoring gagal dimulai atau berhenti karena kesalahan (misalnya, kredensial tidak valid, koneksi gagal), dan sistem menampilkan notifikasi error.

#### **Main Flow (Mengaktifkan Monitor)**
| Langkah | Aksi Aktor (Pengguna) | Respon Sistem |
|---|---|---|
| 1 | Membuka halaman dashboard atau halaman monitoring. | |
| 2 | | Menampilkan status monitor saat ini ("Stopped"). |
| 3 | Menekan tombol "Start Monitor". | |
| 4 | | 1. Memvalidasi konfigurasi API Facebook. <br> 2. Memulai proses (thread) baru di latar belakang. <br> 3. Proses latar belakang masuk ke dalam siklus (loop) monitoring. <br> 4. Memperbarui status di antarmuka menjadi "Running". |
| 5 | Melihat status monitor telah berubah menjadi "Running". | |
| 6 | (Di latar belakang) | Secara periodik (misal: setiap 60 detik): <br> a. Mengambil komentar baru dari Facebook API. <br> b. Menganalisis setiap komentar menggunakan model spam. <br> c. Jika spam dan auto-delete aktif, hapus komentar. <br> d. Jika spam dan auto-delete nonaktif, tambahkan ke daftar "Pending Spam". |

#### **Alternative Flow (Menghentikan Monitor)**
| Langkah | Aksi Aktor (Pengguna) | Respon Sistem |
|---|---|---|
| 1a.1 | Melihat status monitor "Running" dan menekan tombol "Stop Monitor". | |
| 2a.1 | | 1. Mengirim sinyal berhenti ke proses latar belakang. <br> 2. Proses latar belakang menyelesaikan siklus saat ini dan berhenti. <br> 3. Memperbarui status di antarmuka menjadi "Stopped". |
| 3a.1 | Melihat status monitor telah berubah menjadi "Stopped". | |

---

## 2. Activity Diagram

Diagram ini menggambarkan dua bagian: aksi pengguna untuk memulai monitor dan siklus kerja otonom sistem di latar belakang.

```plantuml
@startuml
title Activity Diagram: Monitoring Komentar Otomatis

|Pengguna|
start
:Membuka halaman dashboard;
:Melihat status monitor "Stopped";
:Menekan tombol "Start Monitor";

|Sistem (Antarmuka)|
:Memvalidasi konfigurasi;
if (Konfigurasi valid?) then (Ya)
  :Memulai proses monitor di latar belakang;
  :Update status UI menjadi "Running";
else (Tidak)
  :Tampilkan pesan error;
  stop
endif

|Sistem (Proses Latar Belakang)|
repeat
  :Tunggu sesuai interval waktu;
  :Ambil komentar baru dari Facebook API;
  :Analisis setiap komentar;
  if (Komentar adalah spam?) then (Ya)
    if (Auto-delete aktif?) then (Ya)
      :Hapus komentar via API;
    else (Tidak)
      :Tambahkan ke daftar "Pending Spam";
    endif
  endif
repeat while (Monitor masih berjalan?) is (Ya)

|Sistem (Antarmuka)|
:Status UI tetap "Running";

|Pengguna|
:Melihat monitor berjalan;
stop
@enduml
```

---

## 3. Sequence Diagram

Diagram ini menunjukkan interaksi saat pengguna memulai monitor dan satu siklus kerja dari proses di latar belakang.

```plantuml
@startuml
title Sequence Diagram: Monitoring Komentar Otomatis

actor Pengguna
participant "Antarmuka Web" as Frontend
participant "Monitor Otomatis (Backend)" as Monitor
participant "Model Klasifikasi" as Model
participant "API Facebook" as FB_API

Pengguna -> Frontend: 1. klikStartMonitor()
activate Frontend
Frontend -> Monitor: 2. start()
activate Monitor
Monitor --> Frontend: 3. statusRunning
deactivate Monitor
Frontend -> Pengguna: 4. tampilkanStatus("Running")
deactivate Frontend

loop Setiap X Detik
    Monitor -> FB_API: 5. getNewComments()
    activate Monitor
    activate FB_API
    FB_API --> Monitor: 6. commentList
    deactivate FB_API

    loop untuk setiap komentar
        Monitor -> Model: 7. classify(comment.text)
        activate Model
        Model --> Monitor: 8. result
        deactivate Model

        alt jika result adalah spam
            opt jika auto-delete aktif
                Monitor -> FB_API: 9. deleteComment(comment.id)
                activate FB_API
                FB_API --> Monitor: 10. success
                deactivate FB_API
            else
                Monitor -> Monitor: 11. addToPendingList(comment)
            end
        end
    end
    deactivate Monitor
end

@enduml
```
