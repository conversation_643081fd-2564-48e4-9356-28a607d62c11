# Kebutuhan Fungsional: Pencatatan dan Penampilan Log

Dokumen ini berisi Use Case Description, Activity Diagram, dan Sequence Diagram untuk kebutuhan fungsional: *"Sistem harus mencatat semua aktivitas penting, seperti hasil analisis, per<PERSON><PERSON> pengaturan, dan kes<PERSON>han sistem. Log ini harus dapat dilihat oleh pengguna melalui antarmuka."*

Fokus dari diagram berikut adalah pada bagian **melihat log**, yang merupakan interaksi langsung pengguna dengan sistem.

---

## 1. Use Case Description

### **UC-07: Melihat Log Aktivitas Sistem**

*   **Actor:** Pengguna (User)
*   **Description:** Use case ini menjelaskan bagaimana **Pengguna** mengakses dan melihat daftar catatan (log) dari aktivitas-aktivitas penting yang telah terjadi di dalam sistem.
*   **Preconditions:**
    *   Pengguna telah membuka antarmuka aplikasi.
    *   Sistem telah berjalan dan kemungkinan telah mencatat beberapa aktivitas.
*   **Postconditions:**
    *   **Success:** Daftar log aktivitas sistem ditampilkan kepada **Pengguna** secara kronologis.
    *   **Alternative:** Jika belum ada aktivitas yang tercatat, sebuah pesan yang menginformasikan bahwa log masih kosong akan ditampilkan.

#### **Main Flow (Basic Path)**
| Langkah | Aksi Aktor (Pengguna) | Respon Sistem |
|---|---|---|
| 1 | Membuka atau menavigasi ke halaman "Logs". | |
| 2 | | 1. Menerima permintaan untuk menampilkan log. <br> 2. Mengambil data log dari tempat penyimpanan (misalnya, file log atau state aplikasi). <br> 3. Memformat dan menampilkan data log dalam bentuk daftar yang mudah dibaca. |
| 3 | Melihat daftar log aktivitas yang ditampilkan. | |

#### **Alternative Flow (Log Kosong)**
| Langkah | Aksi Aktor (Pengguna) | Respon Sistem |
|---|---|---|
| 2a.1 | | Mengambil data log dan menemukan bahwa tidak ada catatan. |
| 2a.2 | | Menampilkan pesan di antarmuka, misalnya: "Belum ada aktivitas yang tercatat." |
| - | | Use case berakhir. |

---

## 2. Activity Diagram

Diagram ini menggambarkan alur kerja pengguna saat meminta untuk melihat log sistem.

```plantuml
@startuml
title Activity Diagram: Melihat Log Aktivitas

|Pengguna|
start
:Membuka halaman "Logs";

|Sistem|
:Menerima permintaan;
:Mengambil data log dari penyimpanan;
if (Log tidak kosong?) then (Ya)
  :Menampilkan daftar log
  yang telah diformat;
else (Tidak)
  :Menampilkan pesan "Log masih kosong";
endif

|Pengguna|
:Melihat halaman log;
stop

@enduml
```

---

## 3. Sequence Diagram

Diagram ini menunjukkan interaksi antar komponen saat pengguna meminta untuk melihat halaman log.

```plantuml
@startuml
title Sequence Diagram: Melihat Halaman Log

actor Pengguna
participant "Antarmuka Web" as Frontend
participant "Sistem (Backend)" as Backend
participant "Penyimpanan Log" as LogStorage

Pengguna -> Frontend: 1. klikMenuLogs()
activate Frontend

Frontend -> Backend: 2. getLogData()
activate Backend

Backend -> LogStorage: 3. readLogs()
activate LogStorage
LogStorage --> Backend: 4. logEntries
deactivate LogStorage

Backend --> Frontend: 5. logEntries
deactivate Backend

Frontend -> Frontend: 6. renderLogPage(logEntries)
Frontend --> Pengguna: 7. tampilkanHalamanLogs()
deactivate Frontend

@enduml
```
