# Kebutuhan Fungsional: <PERSON><PERSON><PERSON><PERSON> Daftar Spam

Dokumen ini berisi Activity Diagram, Use Case Description, dan Sequence Diagram untuk kebutuhan fungsional: "Sistem menampilkan daftar item yang telah diidentifikasi sebagai spam. Pengguna harus dapat melihat, menyetujui (konfirmasi sebagai spam), atau menolak (menandai sebagai bukan spam) item dalam daftar ini."

---

## 1. Activity Diagram

Diagram ini menggambarkan alur kerja pengguna saat mengelola item-item yang menunggu tinjauan.

```plantuml
@startuml
title Activity Diagram: Pengelolaan Daftar Spam

|Pengguna|
start
:Membuka halaman "Pending Spam";

|Sistem|
:Mengambil dan menampilkan daftar item
yang ditandai sebagai spam;

|Pengguna|
if (Daftar tidak kosong?) then (Ya)
  :Meninjau item dalam daftar;
  :Me<PERSON><PERSON><PERSON> salah satu item;
  :Memutuskan tindakan: Setujui atau Tolak;
  
  if (<PERSON><PERSON>h "Setujui"?) then (Setujui)
    |Sistem|
    :Memp<PERSON>s persetujuan
    (misal: menghapus komentar);
    :Mencatat tindakan;
  else (Tolak)
    |Sistem|
    :Memproses penolakan
    (misal: menandai sebagai "bukan spam");
    :Mencatat tindakan;
  endif
  
  |Sistem|
  :Memperbarui dan menampilkan kembali
  daftar "Pending Spam";
  
  |Pengguna|
  :Melihat daftar yang telah diperbarui;
else (Tidak)
  |Sistem|
  :Menampilkan pesan "Tidak ada item
  yang perlu ditinjau";
endif

stop
@enduml
```

---

## 2. Use Case Description

### **UC-03: Mengelola Daftar Spam Tertunda**

*   **Actor:** Pengguna (User/Moderator)
*   **Description:** Use case ini menjelaskan bagaimana **Pengguna** meninjau daftar item yang telah diidentifikasi oleh sistem sebagai spam dan kemudian memutuskan untuk menyetujui (mengkonfirmasi sebagai spam) atau menolak (menandai sebagai bukan spam) klasifikasi tersebut.
*   **Preconditions:**
    *   Pengguna telah login dan berada di dalam antarmuka aplikasi.
    *   Terdapat setidaknya satu item dalam antrian "Pending Spam".
*   **Postconditions:**
    *   **Success:** Item yang dipilih telah diproses (misalnya, dihapus jika disetujui, atau statusnya diubah jika ditolak) dan dihapus dari daftar "Pending Spam". Tampilan antarmuka diperbarui.
    *   **Failure:** Tindakan gagal diproses dan sistem menampilkan pesan kesalahan. Item tetap berada di daftar "Pending Spam".

#### **Main Flow (Menyetujui Item sebagai Spam)**
| Langkah | Aksi Aktor (Pengguna) | Respon Sistem |
|---|---|---|
| 1 | Membuka halaman "Pending Spam". | |
| 2 | | Menampilkan daftar item yang menunggu tinjauan. |
| 3 | Mengidentifikasi item yang benar-benar spam dan menekan tombol "Setujui" (atau "Hapus"). | |
| 4 | | 1. Menerima permintaan untuk item yang dipilih. <br> 2. Memproses tindakan (misalnya, memanggil API untuk menghapus komentar). <br> 3. Menghapus item dari daftar internal "Pending Spam". <br> 4. Memperbarui (refresh) tampilan daftar di antarmuka. |
| 5 | Melihat bahwa item tersebut telah hilang dari daftar. | |

#### **Alternative Flow (Menolak Item sebagai Spam)**
| Langkah | Aksi Aktor (Pengguna) | Respon Sistem |
|---|---|---|
| 3a.1 | Mengidentifikasi item yang salah diklasifikasikan dan menekan tombol "Tolak" (atau "Bukan Spam"). | |
| 4a.1 | | 1. Menerima permintaan untuk item yang dipilih. <br> 2. Memproses tindakan (misalnya, mengubah status item menjadi "bukan spam"). <br> 3. Menghapus item dari daftar internal "Pending Spam". <br> 4. Memperbarui (refresh) tampilan daftar di antarmuka. |
| 5a.1 | Melihat bahwa item tersebut telah hilang dari daftar. | |

---

## 3. Sequence Diagram

Diagram ini menunjukkan interaksi antar komponen saat pengguna menyetujui sebuah item sebagai spam.

```plantuml
@startuml
title Sequence Diagram: Menyetujui Item Spam

actor Pengguna
participant "Antarmuka Web" as Frontend
participant "Sistem (Backend)" as Backend
participant "API Eksternal" as API

Pengguna -> Frontend: 1. bukaHalamanPendingSpam()
activate Frontend

Frontend -> Backend: 2. getPendingList()
activate Backend
Backend --> Frontend: 3. pendingList
deactivate Backend

Frontend -> Pengguna: 4. tampilkanDaftar(pendingList)

Pengguna -> Frontend: 5. klikSetujui(itemId, commentId)
Frontend -> Backend: 6. prosesPersetujuan(itemId, commentId)
activate Backend

note right of Backend: Aksi persetujuan di sini adalah\nmenghapus komentar via API
Backend -> API: 7. deleteComment(commentId)
activate API
API --> Backend: 8. statusSukses
deactivate API

Backend -> Backend: 9. hapusItemDariPendingList(itemId)
Backend --> Frontend: 10. konfirmasiSukses
deactivate Backend

Frontend -> Frontend: 11. refreshTampilan()
Frontend -> Pengguna: 12. tampilkanNotifikasi("Komentar berhasil dihapus")
deactivate Frontend

@enduml
```