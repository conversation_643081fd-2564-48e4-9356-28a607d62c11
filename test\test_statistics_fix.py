#!/usr/bin/env python3
"""
Test Statistics Fix
Verify that spam_detected and spam_removed counters are consistent
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.app.streamlit_monitor import AutoMonitor

class MockFacebookAPI:
    """Mock Facebook API for testing"""
    
    def __init__(self):
        self.deletion_count = 0
        self.posts = [{'id': 'test_post', 'message': 'Test post'}]
        self.comments = [
            {'id': 'spam_1', 'message': 'SPAM: Link daftar situs slot gacor!', 'from': {'name': 'Spammer1'}},
            {'id': 'normal_1', 'message': 'Komentar normal biasa', 'from': {'name': 'User1'}},
            {'id': 'spam_2', 'message': 'SPAM: INVESTASI CRYPTO UNTUNG 1000%!', 'from': {'name': 'Spammer2'}},
            {'id': 'normal_2', 'message': 'Terima kasih infonya', 'from': {'name': 'User2'}},
            {'id': 'spam_3', 'message': 'SPAM: MAU IPHONE GRATIS? FOLLOW & SHARE!', 'from': {'name': 'Spammer3'}},
        ]
    
    def get_recent_posts(self, limit=10):
        return self.posts[:limit]
    
    def get_post_comments(self, post_id, limit=50):
        return self.comments[:limit]
    
    def delete_comment(self, comment_id):
        """Mock delete that always succeeds"""
        self.deletion_count += 1
        print(f"🗑️ Mock delete: {comment_id} (total deletions: {self.deletion_count})")
        return True

class MockSpamDetector:
    """Mock spam detector"""
    
    def predict(self, text):
        # Detect spam based on keywords
        is_spam = 'spam' in text.lower()
        confidence = 0.9 if is_spam else 0.1
        
        return {
            'is_spam': is_spam,
            'confidence': confidence,
            'label': 'spam' if is_spam else 'normal'
        }

def test_statistics_consistency():
    """Test that statistics counters are consistent"""
    print("🧪 Testing Statistics Consistency Fix")
    print("=" * 50)
    
    # Setup
    facebook_api = MockFacebookAPI()
    spam_detector = MockSpamDetector()
    
    auto_monitor = AutoMonitor(
        facebook_api=facebook_api,
        spam_detector=spam_detector,
        poll_interval=30
    )
    
    # Show initial statistics
    initial_stats = auto_monitor.get_statistics()
    print(f"📊 Initial Statistics:")
    print(f"  Comments Processed: {initial_stats.get('comments_processed', 0)}")
    print(f"  Spam Detected: {initial_stats.get('spam_detected', 0)}")
    print(f"  Spam Removed: {initial_stats.get('spam_removed', 0)}")
    
    # Expected results
    expected_comments = len(facebook_api.comments)  # 5 comments
    expected_spam = len([c for c in facebook_api.comments if 'spam' in c['message'].lower()])  # 3 spam
    expected_normal = expected_comments - expected_spam  # 2 normal
    
    print(f"\n📋 Expected Results:")
    print(f"  Total Comments: {expected_comments}")
    print(f"  Expected Spam: {expected_spam}")
    print(f"  Expected Normal: {expected_normal}")
    
    # Run scan
    print(f"\n🔄 Running Scan...")
    print("-" * 30)
    auto_monitor._check_for_new_comments()
    
    # Get final statistics
    final_stats = auto_monitor.get_statistics()
    
    print(f"\n📊 Final Statistics:")
    print(f"  Comments Processed: {final_stats.get('comments_processed', 0)}")
    print(f"  Spam Detected: {final_stats.get('spam_detected', 0)}")
    print(f"  Spam Removed: {final_stats.get('spam_removed', 0)}")
    print(f"  Facebook API Deletions: {facebook_api.deletion_count}")
    
    # Validate consistency
    comments_processed = final_stats.get('comments_processed', 0)
    spam_detected = final_stats.get('spam_detected', 0)
    spam_removed = final_stats.get('spam_removed', 0)
    
    print(f"\n🔍 Consistency Validation:")
    print("-" * 30)
    
    # Test 1: Comments processed should match expected
    test1_pass = comments_processed == expected_comments
    print(f"Test 1 - Comments Processed: {comments_processed} == {expected_comments} ✅" if test1_pass else f"Test 1 - Comments Processed: {comments_processed} != {expected_comments} ❌")
    
    # Test 2: Spam detected should match expected
    test2_pass = spam_detected == expected_spam
    print(f"Test 2 - Spam Detected: {spam_detected} == {expected_spam} ✅" if test2_pass else f"Test 2 - Spam Detected: {spam_detected} != {expected_spam} ❌")
    
    # Test 3: Spam removed should match spam detected (all spam should be deleted)
    test3_pass = spam_removed == spam_detected
    print(f"Test 3 - Spam Removed == Spam Detected: {spam_removed} == {spam_detected} ✅" if test3_pass else f"Test 3 - Spam Removed != Spam Detected: {spam_removed} != {spam_detected} ❌")
    
    # Test 4: Spam removed should not exceed spam detected
    test4_pass = spam_removed <= spam_detected
    print(f"Test 4 - Spam Removed <= Spam Detected: {spam_removed} <= {spam_detected} ✅" if test4_pass else f"Test 4 - Spam Removed > Spam Detected: {spam_removed} > {spam_detected} ❌ (IMPOSSIBLE!)")
    
    # Test 5: Facebook API deletion count should match spam removed
    test5_pass = facebook_api.deletion_count == spam_removed
    print(f"Test 5 - API Deletions == Spam Removed: {facebook_api.deletion_count} == {spam_removed} ✅" if test5_pass else f"Test 5 - API Deletions != Spam Removed: {facebook_api.deletion_count} != {spam_removed} ❌")
    
    # Overall result
    all_tests_pass = all([test1_pass, test2_pass, test3_pass, test4_pass, test5_pass])
    
    print(f"\n🎯 Overall Result:")
    print("=" * 20)
    
    if all_tests_pass:
        print("✅ ALL TESTS PASSED!")
        print("✅ Statistics are consistent")
        print("✅ Double counting bug is FIXED")
        print("✅ Spam detected == Spam removed")
    else:
        print("❌ SOME TESTS FAILED!")
        print("❌ Statistics inconsistency detected")
        
        if not test4_pass:
            print("🚨 CRITICAL: Spam removed > Spam detected (impossible scenario)")
        
        if not test3_pass:
            print("⚠️ WARNING: Not all detected spam was removed")
    
    # Test reset functionality
    print(f"\n🔄 Testing Statistics Reset...")
    print("-" * 30)
    
    auto_monitor.reset_statistics()
    reset_stats = auto_monitor.get_statistics()
    
    reset_test_pass = (
        reset_stats.get('comments_processed', -1) == 0 and
        reset_stats.get('spam_detected', -1) == 0 and
        reset_stats.get('spam_removed', -1) == 0
    )
    
    print(f"Reset Test: ✅ Statistics reset successfully" if reset_test_pass else "Reset Test: ❌ Statistics reset failed")
    print(f"  Comments Processed: {reset_stats.get('comments_processed', 0)}")
    print(f"  Spam Detected: {reset_stats.get('spam_detected', 0)}")
    print(f"  Spam Removed: {reset_stats.get('spam_removed', 0)}")
    
    return {
        'all_tests_pass': all_tests_pass,
        'comments_processed': comments_processed,
        'spam_detected': spam_detected,
        'spam_removed': spam_removed,
        'api_deletions': facebook_api.deletion_count,
        'reset_test_pass': reset_test_pass
    }

if __name__ == "__main__":
    result = test_statistics_consistency()
    
    print(f"\n📋 Summary:")
    print("=" * 20)
    
    if result['all_tests_pass'] and result['reset_test_pass']:
        print("🎉 STATISTICS FIX SUCCESSFUL!")
        print("✅ Double counting bug resolved")
        print("✅ Counters are now consistent")
        print("✅ Reset functionality working")
    else:
        print("⚠️ ISSUES STILL EXIST")
        print("❌ Further debugging needed")
    
    print(f"\nFinal Metrics:")
    print(f"  Comments: {result['comments_processed']}")
    print(f"  Spam Detected: {result['spam_detected']}")
    print(f"  Spam Removed: {result['spam_removed']}")
    print(f"  API Calls: {result['api_deletions']}")
