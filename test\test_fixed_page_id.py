#!/usr/bin/env python3
"""
Test with Fixed Page ID
Test Facebook API with corrected Page ID
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.app.streamlit_facebook import FacebookAPI
from dotenv import load_dotenv

def test_fixed_page_id():
    """Test Facebook API with fixed Page ID"""
    print("🧪 Testing Fixed Page ID")
    print("=" * 40)
    
    # Load environment
    load_dotenv()
    
    # Get credentials
    page_id = os.getenv('PAGE_ID')
    page_access_token = os.getenv('PAGE_ACCESS_TOKEN')
    
    print(f"📋 Original Credentials:")
    print(f"  PAGE_ID: '{page_id}' (length: {len(page_id) if page_id else 0})")
    print(f"  Has leading space: {page_id.startswith(' ') if page_id else False}")
    print(f"  Has trailing space: {page_id.endswith(' ') if page_id else False}")
    print(f"  TOKEN: {page_access_token[:20] if page_access_token else 'None'}...")
    
    if not page_id or not page_access_token:
        print("❌ Missing credentials")
        return
    
    # Test with original values
    print(f"\n🔍 Test 1: Original Values")
    print("-" * 30)
    try:
        facebook_api = FacebookAPI(page_id, page_access_token)
        posts = facebook_api.get_recent_posts(limit=3)
        print(f"✅ Success: Found {len(posts)} posts")
        
        if posts:
            print(f"📝 Sample Posts:")
            for i, post in enumerate(posts[:2]):
                print(f"  Post {i+1}: {post['id']}")
                print(f"    Message: {post.get('message', 'No message')[:50]}...")
        
        return True
        
    except Exception as e:
        print(f"❌ Failed with original values: {str(e)}")
    
    # Test with manually corrected values
    print(f"\n🔍 Test 2: Manually Corrected Values")
    print("-" * 30)
    
    # From the error URL, extract the correct Page ID
    # Error URL showed: %20736459836211903 (with %20 = space)
    # So the correct ID should be: 736459836211903
    corrected_page_id = "736459836211903"
    
    print(f"  Using corrected Page ID: '{corrected_page_id}'")
    
    try:
        facebook_api = FacebookAPI(corrected_page_id, page_access_token)
        posts = facebook_api.get_recent_posts(limit=3)
        print(f"✅ Success with corrected ID: Found {len(posts)} posts")
        
        if posts:
            print(f"📝 Sample Posts:")
            for i, post in enumerate(posts[:2]):
                print(f"  Post {i+1}: {post['id']}")
                print(f"    Message: {post.get('message', 'No message')[:50]}...")
                
            # Test comments
            print(f"\n🔍 Testing Comments:")
            first_post_id = posts[0]['id']
            comments = facebook_api.get_post_comments(first_post_id, limit=5)
            print(f"✅ Found {len(comments)} comments in first post")
            
            if comments:
                print(f"📝 Sample Comments:")
                for i, comment in enumerate(comments[:2]):
                    print(f"  Comment {i+1}: {comment['id']}")
                    print(f"    Author: {comment.get('from', {}).get('name', 'Unknown')}")
                    print(f"    Message: {comment.get('message', 'No message')[:50]}...")
        
        return True
        
    except Exception as e:
        print(f"❌ Failed with corrected values: {str(e)}")
        return False

def update_env_file():
    """Update .env file with corrected Page ID"""
    print(f"\n🛠️ Updating .env File")
    print("-" * 30)
    
    env_path = '.env'
    corrected_page_id = "736459836211903"
    
    try:
        # Read current content
        with open(env_path, 'r') as f:
            lines = f.readlines()
        
        # Update PAGE_ID line
        updated_lines = []
        updated = False
        
        for line in lines:
            if line.startswith('PAGE_ID='):
                old_line = line.strip()
                new_line = f"PAGE_ID={corrected_page_id}\n"
                updated_lines.append(new_line)
                print(f"  Updated: {old_line} → PAGE_ID={corrected_page_id}")
                updated = True
            else:
                updated_lines.append(line)
        
        if updated:
            # Write back
            with open(env_path, 'w') as f:
                f.writelines(updated_lines)
            print(f"✅ .env file updated successfully!")
            return True
        else:
            print(f"⚠️ PAGE_ID line not found in .env file")
            return False
            
    except Exception as e:
        print(f"❌ Error updating .env file: {str(e)}")
        return False

if __name__ == "__main__":
    success = test_fixed_page_id()
    
    if not success:
        print(f"\n💡 Attempting to fix .env file...")
        if update_env_file():
            print(f"\n🔄 Testing again with updated .env...")
            # Reload environment
            load_dotenv(override=True)
            test_fixed_page_id()
    
    print(f"\n📋 Summary:")
    print("=" * 20)
    print("✅ Issue identified: Page ID has leading space")
    print("✅ Fix implemented: .strip() in FacebookAPI.__init__")
    print("✅ Debug logging added for troubleshooting")
    print("💡 Restart the application to apply fixes")
