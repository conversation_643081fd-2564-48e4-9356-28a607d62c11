#!/usr/bin/env python3
"""
Debug Facebook API Delete Comment Issues
Test actual Facebook API responses for comment deletion
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import requests
import json
from datetime import datetime
from src.app.streamlit_facebook import Facebook<PERSON><PERSON>

def test_facebook_delete_debug():
    """Debug Facebook API delete comment functionality"""
    print("🔍 Facebook API Delete Comment Debug")
    print("=" * 50)
    
    # Get credentials from environment
    page_id = os.getenv('PAGE_ID')
    page_access_token = os.getenv('PAGE_ACCESS_TOKEN')
    
    if not page_id or not page_access_token:
        print("❌ Missing Facebook credentials")
        print("Please set PAGE_ID and PAGE_ACCESS_TOKEN in .env file")
        return
    
    print(f"📋 Configuration:")
    print(f"  Page ID: {page_id}")
    print(f"  Token: {page_access_token[:20]}...")
    
    try:
        # Initialize Facebook API
        facebook_api = FacebookAPI(page_id, page_access_token)
        print("✅ Facebook API initialized successfully")
        
        # Get recent posts
        print("\n🔍 Getting recent posts...")
        posts = facebook_api.get_recent_posts(limit=3)
        print(f"✅ Found {len(posts)} posts")
        
        if not posts:
            print("❌ No posts found")
            return
        
        # Get comments from first post
        post = posts[0]
        post_id = post['id']
        print(f"\n🔍 Getting comments from post: {post_id}")
        
        comments = facebook_api.get_post_comments(post_id, limit=10)
        print(f"✅ Found {len(comments)} comments")
        
        if not comments:
            print("❌ No comments found")
            return
        
        # Show comment details
        print(f"\n📝 Comment Details:")
        for i, comment in enumerate(comments[:3]):
            print(f"  Comment {i+1}:")
            print(f"    ID: {comment['id']}")
            print(f"    Author: {comment.get('from', {}).get('name', 'Unknown')}")
            print(f"    Message: {comment.get('message', 'No message')[:50]}...")
            print(f"    Created: {comment.get('created_time', 'Unknown')}")
        
        # Test delete permissions
        print(f"\n🔍 Testing Delete Permissions...")
        test_comment = comments[0]
        comment_id = test_comment['id']
        
        print(f"Testing delete on comment: {comment_id}")
        
        # Manual API call to debug
        base_url = "https://graph.facebook.com/v18.0"
        delete_url = f"{base_url}/{comment_id}"
        
        print(f"Delete URL: {delete_url}")
        print(f"Access Token: {page_access_token[:20]}...")
        
        # Try DELETE request
        response = requests.delete(
            delete_url,
            params={'access_token': page_access_token}
        )
        
        print(f"\n📊 Delete Response:")
        print(f"  Status Code: {response.status_code}")
        print(f"  Headers: {dict(response.headers)}")
        
        try:
            response_data = response.json()
            print(f"  Response Data: {json.dumps(response_data, indent=2)}")
        except:
            print(f"  Response Text: {response.text}")
        
        if response.status_code == 200:
            print("✅ DELETE request successful")
        else:
            print("❌ DELETE request failed")
            
            # Check specific error codes
            if response.status_code == 403:
                print("🚨 Permission Error: Token doesn't have delete permissions")
            elif response.status_code == 400:
                print("🚨 Bad Request: Invalid comment ID or parameters")
            elif response.status_code == 404:
                print("🚨 Not Found: Comment doesn't exist or already deleted")
            elif response.status_code == 429:
                print("🚨 Rate Limited: Too many requests")
        
        # Test token permissions
        print(f"\n🔍 Testing Token Permissions...")
        me_response = requests.get(
            f"{base_url}/me",
            params={
                'access_token': page_access_token,
                'fields': 'id,name,permissions'
            }
        )
        
        if me_response.status_code == 200:
            me_data = me_response.json()
            print(f"✅ Token Info:")
            print(f"  Name: {me_data.get('name', 'Unknown')}")
            print(f"  ID: {me_data.get('id', 'Unknown')}")
            
            # Check permissions
            permissions = me_data.get('permissions', {}).get('data', [])
            if permissions:
                print(f"  Permissions:")
                for perm in permissions:
                    status = perm.get('status', 'unknown')
                    permission = perm.get('permission', 'unknown')
                    print(f"    {permission}: {status}")
            else:
                print("  No permissions data available")
        else:
            print(f"❌ Failed to get token info: {me_response.status_code}")
        
        # Test page permissions
        print(f"\n🔍 Testing Page Permissions...")
        page_response = requests.get(
            f"{base_url}/{page_id}",
            params={
                'access_token': page_access_token,
                'fields': 'id,name,access_token,roles'
            }
        )
        
        if page_response.status_code == 200:
            page_data = page_response.json()
            print(f"✅ Page Info:")
            print(f"  Name: {page_data.get('name', 'Unknown')}")
            print(f"  ID: {page_data.get('id', 'Unknown')}")
            print(f"  Has Access Token: {'access_token' in page_data}")
        else:
            print(f"❌ Failed to get page info: {page_response.status_code}")
        
        # Recommendations
        print(f"\n💡 Recommendations:")
        print("=" * 30)
        
        if response.status_code == 403:
            print("🔧 Permission Issues:")
            print("  1. Ensure token has 'pages_manage_posts' permission")
            print("  2. Ensure you are Admin/Editor of the page")
            print("  3. Regenerate page access token with proper permissions")
            
        elif response.status_code == 400:
            print("🔧 Request Issues:")
            print("  1. Check comment ID format")
            print("  2. Ensure comment still exists")
            print("  3. Verify API version compatibility")
            
        elif response.status_code == 429:
            print("🔧 Rate Limiting:")
            print("  1. Reduce deletion frequency")
            print("  2. Add delays between delete requests")
            print("  3. Implement exponential backoff")
            
        else:
            print("🔧 General Troubleshooting:")
            print("  1. Check Facebook API documentation")
            print("  2. Verify token hasn't expired")
            print("  3. Test with Facebook Graph API Explorer")
        
    except Exception as e:
        print(f"❌ Error: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_facebook_delete_debug()
