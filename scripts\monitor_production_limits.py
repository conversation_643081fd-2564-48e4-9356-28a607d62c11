#!/usr/bin/env python3
"""
Production Rate Limit Monitor
Monitor rate limits dalam production environment
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import time
import requests
import json
from datetime import datetime, timedelta
from dotenv import load_dotenv
import threading
from collections import deque

class ProductionRateLimitMonitor:
    """Monitor rate limits untuk production environment"""
    
    def __init__(self):
        """Initialize production monitor"""
        load_dotenv()
        
        self.page_id = os.getenv('PAGE_ID')
        self.access_token = os.getenv('PAGE_ACCESS_TOKEN')
        self.base_url = "https://graph.facebook.com/v18.0"
        
        # Monitoring data
        self.usage_history = deque(maxlen=100)  # Keep last 100 measurements
        self.rate_limit_events = []
        self.monitoring = False
        
        if not self.page_id or not self.access_token:
            raise ValueError("PAGE_ID dan PAGE_ACCESS_TOKEN harus diset di .env")
    
    def get_current_usage(self):
        """Get current API usage from Facebook"""
        try:
            response = requests.get(
                f"{self.base_url}/{self.page_id}",
                params={'access_token': self.access_token, 'fields': 'id'},
                timeout=10
            )
            
            usage_data = {
                'timestamp': datetime.now(),
                'status_code': response.status_code,
                'response_time': response.elapsed.total_seconds(),
                'rate_limit_headers': {}
            }
            
            # Extract rate limit headers
            headers_to_check = [
                'X-App-Usage',
                'X-Page-Usage', 
                'X-Business-Use-Case-Usage',
                'Retry-After'
            ]
            
            for header in headers_to_check:
                value = response.headers.get(header)
                if value:
                    usage_data['rate_limit_headers'][header] = value
            
            # Parse business usage
            business_usage = response.headers.get('X-Business-Use-Case-Usage')
            if business_usage:
                try:
                    parsed_usage = json.loads(business_usage)
                    usage_data['parsed_business_usage'] = parsed_usage
                    
                    # Extract key metrics
                    for app_id, usage_list in parsed_usage.items():
                        for usage in usage_list:
                            usage_data['call_count'] = usage.get('call_count', 0)
                            usage_data['total_time'] = usage.get('total_time', 0)
                            usage_data['total_cputime'] = usage.get('total_cputime', 0)
                            usage_data['estimated_time_to_regain_access'] = usage.get('estimated_time_to_regain_access', 0)
                            break
                        break
                except:
                    pass
            
            # Check for rate limiting
            if response.status_code == 429:
                self.rate_limit_events.append(usage_data)
            
            return usage_data
            
        except Exception as e:
            return {
                'timestamp': datetime.now(),
                'error': str(e),
                'status_code': 0
            }
    
    def monitor_continuous(self, duration_minutes=60, interval_seconds=30):
        """Monitor rate limits continuously"""
        print(f"🔍 Starting Production Rate Limit Monitor")
        print(f"Duration: {duration_minutes} minutes")
        print(f"Check interval: {interval_seconds} seconds")
        print("=" * 60)
        
        self.monitoring = True
        start_time = datetime.now()
        end_time = start_time + timedelta(minutes=duration_minutes)
        
        check_count = 0
        
        try:
            while datetime.now() < end_time and self.monitoring:
                check_count += 1
                
                # Get current usage
                usage = self.get_current_usage()
                self.usage_history.append(usage)
                
                # Display current status
                elapsed = (datetime.now() - start_time).total_seconds() / 60
                print(f"\n⏰ Check #{check_count} - {elapsed:.1f}min elapsed")
                
                if 'error' in usage:
                    print(f"❌ Error: {usage['error']}")
                else:
                    print(f"✅ Status: {usage['status_code']} ({usage['response_time']:.3f}s)")
                    
                    # Show usage metrics
                    if 'call_count' in usage:
                        call_count = usage['call_count']
                        total_time = usage['total_time']
                        total_cputime = usage['total_cputime']
                        
                        print(f"📊 Usage: Calls {call_count}%, Time {total_time}%, CPU {total_cputime}%")
                        
                        # Alert on high usage
                        if call_count > 80:
                            print(f"🚨 HIGH CALL COUNT: {call_count}%")
                        elif call_count > 60:
                            print(f"⚠️ MODERATE CALL COUNT: {call_count}%")
                        
                        if total_time > 80:
                            print(f"🚨 HIGH TIME USAGE: {total_time}%")
                        elif total_time > 60:
                            print(f"⚠️ MODERATE TIME USAGE: {total_time}%")
                    
                    # Show rate limit headers
                    if usage['rate_limit_headers']:
                        print(f"📈 Headers: {usage['rate_limit_headers']}")
                
                # Wait for next check
                time.sleep(interval_seconds)
        
        except KeyboardInterrupt:
            print(f"\n⏹️ Monitoring stopped by user")
        
        finally:
            self.monitoring = False
            self.generate_monitoring_report(start_time, check_count)
    
    def generate_monitoring_report(self, start_time, check_count):
        """Generate monitoring report"""
        print(f"\n📋 PRODUCTION MONITORING REPORT")
        print("=" * 60)
        
        duration = (datetime.now() - start_time).total_seconds() / 60
        
        print(f"📊 Monitoring Summary:")
        print(f"   Duration: {duration:.1f} minutes")
        print(f"   Total Checks: {check_count}")
        print(f"   Check Frequency: {check_count/duration:.1f} checks/minute")
        
        # Analyze usage data
        if self.usage_history:
            valid_usage = [u for u in self.usage_history if 'call_count' in u]
            
            if valid_usage:
                call_counts = [u['call_count'] for u in valid_usage]
                time_usage = [u['total_time'] for u in valid_usage]
                cpu_usage = [u['total_cputime'] for u in valid_usage]
                
                print(f"\n📈 Usage Statistics:")
                print(f"   Call Count - Min: {min(call_counts)}%, Max: {max(call_counts)}%, Avg: {sum(call_counts)/len(call_counts):.1f}%")
                print(f"   Time Usage - Min: {min(time_usage)}%, Max: {max(time_usage)}%, Avg: {sum(time_usage)/len(time_usage):.1f}%")
                print(f"   CPU Usage - Min: {min(cpu_usage)}%, Max: {max(cpu_usage)}%, Avg: {sum(cpu_usage)/len(cpu_usage):.1f}%")
                
                # Trend analysis
                if len(call_counts) >= 3:
                    recent_avg = sum(call_counts[-3:]) / 3
                    early_avg = sum(call_counts[:3]) / 3
                    
                    if recent_avg > early_avg + 10:
                        print(f"📈 TREND: Usage increasing ({early_avg:.1f}% → {recent_avg:.1f}%)")
                    elif recent_avg < early_avg - 10:
                        print(f"📉 TREND: Usage decreasing ({early_avg:.1f}% → {recent_avg:.1f}%)")
                    else:
                        print(f"📊 TREND: Usage stable (~{recent_avg:.1f}%)")
        
        # Rate limit events
        if self.rate_limit_events:
            print(f"\n🚨 Rate Limit Events: {len(self.rate_limit_events)}")
            for i, event in enumerate(self.rate_limit_events):
                print(f"   Event {i+1}: {event['timestamp'].strftime('%H:%M:%S')}")
        else:
            print(f"\n✅ No rate limit events detected")
        
        # Recommendations
        print(f"\n💡 Recommendations:")
        
        if valid_usage:
            max_call_count = max(call_counts)
            max_time_usage = max(time_usage)
            
            if max_call_count > 90:
                print(f"   🚨 CRITICAL: Call count reached {max_call_count}% - immediate action required")
                print(f"   💡 Reduce request frequency by 50%")
                print(f"   💡 Implement longer delays between operations")
            elif max_call_count > 70:
                print(f"   ⚠️ WARNING: Call count reached {max_call_count}% - monitor closely")
                print(f"   💡 Consider reducing request frequency by 25%")
            elif max_call_count > 50:
                print(f"   ℹ️ INFO: Call count reached {max_call_count}% - within acceptable range")
            else:
                print(f"   ✅ Call count usage is low ({max_call_count}%) - safe to continue")
            
            if max_time_usage > 90:
                print(f"   🚨 CRITICAL: Time usage reached {max_time_usage}% - optimize requests")
            elif max_time_usage > 70:
                print(f"   ⚠️ WARNING: Time usage reached {max_time_usage}% - monitor performance")
        
        # Save detailed report
        self.save_detailed_report(start_time, duration, check_count)
    
    def save_detailed_report(self, start_time, duration, check_count):
        """Save detailed report to file"""
        try:
            report_data = {
                'monitoring_session': {
                    'start_time': start_time.isoformat(),
                    'duration_minutes': duration,
                    'total_checks': check_count
                },
                'usage_history': [
                    {
                        'timestamp': u['timestamp'].isoformat(),
                        'call_count': u.get('call_count', 0),
                        'total_time': u.get('total_time', 0),
                        'total_cputime': u.get('total_cputime', 0),
                        'status_code': u.get('status_code', 0),
                        'response_time': u.get('response_time', 0)
                    }
                    for u in self.usage_history if 'timestamp' in u
                ],
                'rate_limit_events': [
                    {
                        'timestamp': e['timestamp'].isoformat(),
                        'status_code': e.get('status_code', 0),
                        'headers': e.get('rate_limit_headers', {})
                    }
                    for e in self.rate_limit_events
                ]
            }
            
            filename = f"rate_limit_report_{start_time.strftime('%Y%m%d_%H%M%S')}.json"
            filepath = os.path.join('logs', filename)
            
            # Create logs directory if it doesn't exist
            os.makedirs('logs', exist_ok=True)
            
            with open(filepath, 'w') as f:
                json.dump(report_data, f, indent=2)
            
            print(f"\n💾 Detailed report saved: {filepath}")
            
        except Exception as e:
            print(f"\n⚠️ Failed to save detailed report: {str(e)}")

def main():
    """Main function"""
    print("🔍 Production Rate Limit Monitor")
    print("=" * 60)
    
    try:
        monitor = ProductionRateLimitMonitor()
        
        # Configuration
        print("Configuration options:")
        print("1. Quick check (5 minutes, 30s interval)")
        print("2. Standard monitoring (30 minutes, 60s interval)")
        print("3. Extended monitoring (60 minutes, 120s interval)")
        print("4. Custom configuration")
        
        choice = input("\nSelect option (1-4): ").strip()
        
        if choice == "1":
            duration, interval = 5, 30
        elif choice == "2":
            duration, interval = 30, 60
        elif choice == "3":
            duration, interval = 60, 120
        elif choice == "4":
            try:
                duration = int(input("Duration (minutes): "))
                interval = int(input("Check interval (seconds): "))
            except:
                print("Invalid input, using defaults")
                duration, interval = 30, 60
        else:
            duration, interval = 30, 60
        
        print(f"\n🚀 Starting monitoring...")
        print(f"Press Ctrl+C to stop early")
        
        monitor.monitor_continuous(duration, interval)
        
    except KeyboardInterrupt:
        print("\n⏹️ Monitoring interrupted by user")
    except Exception as e:
        print(f"\n❌ Monitoring failed: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
