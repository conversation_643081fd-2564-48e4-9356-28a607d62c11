#!/usr/bin/env python3
"""
Test Simple Monitor (No Enhanced Scanning)
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.app.streamlit_monitor import AutoMonitor

class MockFacebookAPI:
    def get_recent_posts(self, limit=5):
        return [{'id': 'test_post_1', 'message': 'Test post'}]
    
    def get_post_comments(self, post_id, limit=50):
        return [
            {'id': 'comment_1', 'message': 'Normal comment', 'from': {'name': 'User1'}},
            {'id': 'comment_2', 'message': 'Spam comment buy now!', 'from': {'name': 'User2'}}
        ]
    
    def delete_comment(self, comment_id):
        print(f"🗑️ MOCK DELETE: {comment_id}")
        return True

class MockSpamDetector:
    def predict(self, text):
        is_spam = 'spam' in text.lower() or 'buy now' in text.lower()
        return {
            'is_spam': is_spam,
            'confidence': 0.9 if is_spam else 0.1,
            'label': 'spam' if is_spam else 'normal'
        }

def test_simple_monitor():
    print("🧪 Testing Simple Monitor (No Enhanced Scanning)")
    print("=" * 50)
    
    # Setup
    facebook_api = MockFacebookAPI()
    spam_detector = MockSpamDetector()
    
    # Create monitor
    auto_monitor = AutoMonitor(
        facebook_api=facebook_api,
        spam_detector=spam_detector,
        poll_interval=30
    )
    
    print("📋 Configuration:")
    config = auto_monitor.get_config()
    for key, value in config.items():
        print(f"  {key}: {value}")
    
    print("\n🔄 Testing Regular Scan...")
    auto_monitor._check_for_new_comments()
    
    stats = auto_monitor.statistics
    print(f"\n📊 Results:")
    print(f"  Comments Processed: {stats['comments_processed']}")
    print(f"  Spam Removed: {stats['spam_removed']}")
    print(f"  Processed Comments Cache: {len(auto_monitor.processed_comments)}")
    
    print("\n✅ Simple Monitor Test Completed!")
    print("🎯 Features Working:")
    print("  • Regular scanning (50 comments per post)")
    print("  • Spam detection and deletion")
    print("  • Statistics tracking")
    print("  • No enhanced scanning features")

if __name__ == "__main__":
    test_simple_monitor()
