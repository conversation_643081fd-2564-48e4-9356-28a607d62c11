#!/usr/bin/env python3
"""
Test Old vs New Comments Deletion
Menguji apakah komentar lama dan baru sama-sama terhapus
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from datetime import datetime, timedelta
from src.app.streamlit_monitor import AutoMonitor

class MockFacebookAPI:
    """Mock Facebook API dengan tracking deletion"""
    
    def __init__(self):
        # Track deleted comments
        self.deleted_comments = []
        self.deletion_attempts = []
        
        # Create posts
        self.posts = [{'id': 'test_post', 'message': 'Test post'}]
        
        # Create comments with different ages
        base_time = datetime.now()
        self.comments = [
            # OLD COMMENTS (7 days ago)
            {
                'id': 'old_spam_1',
                'message': 'SPAM LAMA: Link daftar situs slot gacor ada di bio!',
                'from': {'name': 'OldSpammer1'},
                'created_time': (base_time - timedelta(days=7)).isoformat(),
                'age': 'OLD',
                'is_spam': True
            },
            {
                'id': 'old_normal_1',
                'message': 'Komentar normal lama: Terima kasih infonya',
                'from': {'name': 'OldUser1'},
                'created_time': (base_time - timedelta(days=6)).isoformat(),
                'age': 'OLD',
                'is_spam': False
            },
            {
                'id': 'old_spam_2',
                'message': 'SPAM LAMA: INVESTASI CRYPTO PASTI UNTUNG 1000%!',
                'from': {'name': 'OldSpammer2'},
                'created_time': (base_time - timedelta(days=5)).isoformat(),
                'age': 'OLD',
                'is_spam': True
            },
            
            # MEDIUM COMMENTS (2 days ago)
            {
                'id': 'med_spam_1',
                'message': 'SPAM SEDANG: Bonus new member 200%, klaim sekarang!',
                'from': {'name': 'MedSpammer1'},
                'created_time': (base_time - timedelta(days=2)).isoformat(),
                'age': 'MEDIUM',
                'is_spam': True
            },
            {
                'id': 'med_normal_1',
                'message': 'Komentar normal sedang: Bagus nih kontennya',
                'from': {'name': 'MedUser1'},
                'created_time': (base_time - timedelta(days=1)).isoformat(),
                'age': 'MEDIUM',
                'is_spam': False
            },
            
            # NEW COMMENTS (today)
            {
                'id': 'new_spam_1',
                'message': 'SPAM BARU: MAU PENGHASILAN TAMBAHAN? KLIK LINK BIO!',
                'from': {'name': 'NewSpammer1'},
                'created_time': (base_time - timedelta(hours=2)).isoformat(),
                'age': 'NEW',
                'is_spam': True
            },
            {
                'id': 'new_normal_1',
                'message': 'Komentar normal baru: Setuju banget dengan pendapat ini',
                'from': {'name': 'NewUser1'},
                'created_time': (base_time - timedelta(hours=1)).isoformat(),
                'age': 'NEW',
                'is_spam': False
            },
            {
                'id': 'new_spam_2',
                'message': 'SPAM BARU: VIDEO VIRAL TERBARU NO SENSOR!',
                'from': {'name': 'NewSpammer2'},
                'created_time': base_time.isoformat(),
                'age': 'NEW',
                'is_spam': True
            }
        ]
        
        # Sort by creation time (newest first, like Facebook API)
        self.comments.sort(key=lambda x: x['created_time'], reverse=True)
    
    def get_recent_posts(self, limit=10):
        return self.posts[:limit]
    
    def get_post_comments(self, post_id, limit=50):
        return self.comments[:limit]
    
    def delete_comment(self, comment_id):
        """Mock delete with detailed tracking"""
        self.deletion_attempts.append(comment_id)
        
        # Find the comment
        comment = next((c for c in self.comments if c['id'] == comment_id), None)
        if comment:
            self.deleted_comments.append({
                'id': comment_id,
                'age': comment['age'],
                'is_spam': comment['is_spam'],
                'message': comment['message'][:50],
                'deleted_at': datetime.now().isoformat()
            })
            print(f"🗑️ DELETED {comment['age']} {'SPAM' if comment['is_spam'] else 'NORMAL'}: {comment_id}")
            return True
        else:
            print(f"❌ FAILED TO DELETE: {comment_id} (not found)")
            return False

class MockSpamDetector:
    """Mock spam detector"""
    
    def predict(self, text):
        # Detect spam based on keywords
        spam_keywords = ['spam', 'slot', 'gacor', 'bonus', 'crypto', 'untung', 
                        'penghasilan', 'link bio', 'video viral', 'no sensor']
        
        text_lower = text.lower()
        spam_score = sum(1 for keyword in spam_keywords if keyword in text_lower)
        
        if spam_score >= 2:
            confidence = 0.9
            is_spam = True
        elif spam_score == 1:
            confidence = 0.7
            is_spam = True
        else:
            confidence = 0.1
            is_spam = False
        
        return {
            'is_spam': is_spam,
            'confidence': confidence,
            'label': 'spam' if is_spam else 'normal'
        }

def test_old_vs_new_deletion():
    """Test deletion of old vs new comments"""
    print("🧪 Testing Old vs New Comments Deletion")
    print("=" * 50)
    
    # Setup
    facebook_api = MockFacebookAPI()
    spam_detector = MockSpamDetector()
    
    auto_monitor = AutoMonitor(
        facebook_api=facebook_api,
        spam_detector=spam_detector,
        poll_interval=30
    )
    
    # Show initial comments
    print("📋 Initial Comments Setup:")
    for comment in facebook_api.comments:
        spam_status = "SPAM" if comment['is_spam'] else "NORMAL"
        print(f"  {comment['age']:6} {spam_status:6}: {comment['id']} - {comment['message'][:40]}...")
    
    # Count initial spam by age
    old_spam = [c for c in facebook_api.comments if c['age'] == 'OLD' and c['is_spam']]
    med_spam = [c for c in facebook_api.comments if c['age'] == 'MEDIUM' and c['is_spam']]
    new_spam = [c for c in facebook_api.comments if c['age'] == 'NEW' and c['is_spam']]
    
    print(f"\n📊 Initial Spam Count:")
    print(f"  OLD Spam: {len(old_spam)}")
    print(f"  MEDIUM Spam: {len(med_spam)}")
    print(f"  NEW Spam: {len(new_spam)}")
    print(f"  TOTAL Spam: {len(old_spam) + len(med_spam) + len(new_spam)}")
    
    # Run scan
    print(f"\n🔄 Running Scan...")
    print("-" * 30)
    auto_monitor._check_for_new_comments()
    
    # Analyze results
    stats = auto_monitor.statistics
    deleted_comments = facebook_api.deleted_comments
    
    print(f"\n📊 Scan Results:")
    print(f"  Comments Processed: {stats['comments_processed']}")
    print(f"  Spam Detected: {stats.get('spam_detected', 0)}")
    print(f"  Spam Removed: {stats['spam_removed']}")
    print(f"  Deletion Attempts: {len(facebook_api.deletion_attempts)}")
    print(f"  Successful Deletions: {len(deleted_comments)}")
    
    # Analyze deletions by age
    old_deleted = [d for d in deleted_comments if d['age'] == 'OLD']
    med_deleted = [d for d in deleted_comments if d['age'] == 'MEDIUM']
    new_deleted = [d for d in deleted_comments if d['age'] == 'NEW']
    
    print(f"\n📈 Deletions by Age:")
    print(f"  OLD Comments Deleted: {len(old_deleted)}/{len(old_spam)} ({len(old_deleted)/len(old_spam)*100:.0f}%)")
    print(f"  MEDIUM Comments Deleted: {len(med_deleted)}/{len(med_spam)} ({len(med_deleted)/len(med_spam)*100:.0f}%)")
    print(f"  NEW Comments Deleted: {len(new_deleted)}/{len(new_spam)} ({len(new_deleted)/len(new_spam)*100:.0f}%)")
    
    # Show detailed deletion results
    print(f"\n🗑️ Detailed Deletion Results:")
    for deleted in deleted_comments:
        print(f"  ✅ {deleted['age']:6} SPAM: {deleted['id']} - {deleted['message']}...")
    
    # Check for missed spam
    all_spam_ids = [c['id'] for c in facebook_api.comments if c['is_spam']]
    deleted_spam_ids = [d['id'] for d in deleted_comments if d['is_spam']]
    missed_spam_ids = [sid for sid in all_spam_ids if sid not in deleted_spam_ids]
    
    if missed_spam_ids:
        print(f"\n⚠️ MISSED SPAM:")
        for missed_id in missed_spam_ids:
            missed_comment = next(c for c in facebook_api.comments if c['id'] == missed_id)
            print(f"  ❌ {missed_comment['age']:6} SPAM: {missed_id} - {missed_comment['message'][:40]}...")
    else:
        print(f"\n✅ NO MISSED SPAM: All spam comments were deleted!")
    
    # Final analysis
    print(f"\n🎯 Analysis:")
    print("-" * 20)
    
    total_spam = len(all_spam_ids)
    total_deleted_spam = len([d for d in deleted_comments if d['is_spam']])
    deletion_rate = (total_deleted_spam / total_spam * 100) if total_spam > 0 else 0
    
    print(f"Overall Spam Deletion Rate: {deletion_rate:.1f}% ({total_deleted_spam}/{total_spam})")
    
    if deletion_rate >= 100:
        print("✅ PERFECT: All spam deleted regardless of age")
    elif deletion_rate >= 80:
        print("✅ GOOD: Most spam deleted")
    elif deletion_rate >= 60:
        print("⚠️ FAIR: Some spam missed")
    else:
        print("❌ POOR: Many spam missed")
    
    # Check age bias
    old_rate = (len(old_deleted) / len(old_spam) * 100) if len(old_spam) > 0 else 0
    new_rate = (len(new_deleted) / len(new_spam) * 100) if len(new_spam) > 0 else 0
    
    if abs(old_rate - new_rate) > 20:
        print(f"⚠️ AGE BIAS DETECTED: Old spam {old_rate:.0f}% vs New spam {new_rate:.0f}%")
    else:
        print(f"✅ NO AGE BIAS: Old and new spam deleted equally")
    
    return {
        'total_spam': total_spam,
        'deleted_spam': total_deleted_spam,
        'deletion_rate': deletion_rate,
        'old_deleted': len(old_deleted),
        'new_deleted': len(new_deleted),
        'missed_spam': len(missed_spam_ids)
    }

if __name__ == "__main__":
    test_old_vs_new_deletion()
